package com.demo.service;

import com.demo.entity.VO.AttendanceScheduleVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AttendanceScheduleService {
    // 获取员工的考勤排班信息
    List<AttendanceScheduleVO> getEmployeeAttendanceSchedule(String userName, LocalDateTime startDate, LocalDateTime endDate);
    
    // 处理打卡逻辑
    AttendanceScheduleVO handleClock(Integer userId);
    
    // 获取考勤统计
    Map<String, Object> getAttendanceStats(Integer userId, LocalDateTime startDate, LocalDateTime endDate);
} 