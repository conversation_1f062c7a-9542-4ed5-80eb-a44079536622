<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.RoleUsersMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.RoleUsers">
    <!--@mbg.generated-->
    <!--@Table role_users-->
    <id column="ru_id" jdbcType="INTEGER" property="ruId" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ru_id, role_id, user_id
  </sql>

  <insert id="insertRole">
    insert into role_users(role_id, user_id) values
    <foreach collection="role" item="roleId" separator=",">
      (#{roleId}, #{insertedUserId})
    </foreach>
  </insert>


</mapper>