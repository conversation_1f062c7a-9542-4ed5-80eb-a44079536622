package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户表
 */
@Data
@TableName(value = "`users`")
public class Users {
    /**
     * id
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Integer userId;

    /**
     * 电话
     */
    @TableField(value = "`phone`")
    private String phone;

    /**
     * 密码
     */
    @TableField(value = "`password`")
    private String password;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 性别
     */
    @TableField(value = "`sex`")
    private String sex;

    /**
     * 部门
     */
    @TableField(value = "`dept_name`")
    private String deptName;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡村（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 点位
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 状态（0正常1冻结2删除)
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "`create_time`")
    private LocalDateTime createTime;

    @TableField(exist = false)
    private List<Role> roles;
}