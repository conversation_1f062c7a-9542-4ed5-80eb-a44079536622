package com.demo.enums;

import org.apache.ibatis.type.TypeHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan("com.demo.mapper") // 替换为你的Mapper包路径
public class MyBatisConfig {

    @Bean
    public TypeHandler<VehicleTypeEnum> overcrowdingEnumTypeHandler() {
        return new VehicleTypeEnumTypeHandler();
    }

    @Bean
    public TypeHandler<PersuasionEnum> persuasionEnumTypeHandler() {
        return new PersuasionEnumTypeHandler();
    }

    @Bean
    public TypeHandler<TrafficEnum> trafficEnumTypeHandler() {
        return new TrafficEnumTypeHandler();
    }

    @Bean
    public TypeHandler<HandleEnum> handleEnumTypeHandler() {
        return new HandleEnumTypeHandler();
    }

    @Bean
    public TypeHandler<PlateColorEnum> plateColorEnumTypeHandler() {
        return new PlateColorEnumTypeHandler();
    }
}
