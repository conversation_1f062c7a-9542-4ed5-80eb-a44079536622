package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.demo.service.LeavePostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RestController
@RequestMapping("/leave-post")
public class LeavePostController {
    
    @Autowired
    private LeavePostService leavePostService;
    
    /**
     * 获取脱岗统计
     */
    @GetMapping("/stats")
    @SaCheckPermission("attendance:view")
    public SaResult getLeavePostStats(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return leavePostService.getLeavePostStats(city, county, township, hamlet, site, date);
    }
    
    /**
     * 获取用户脱岗记录
     */
    @GetMapping("/user")
    @SaCheckPermission("attendance:view")
    public SaResult getUserLeavePostsByDate(
            @RequestParam Integer userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return leavePostService.getUserLeavePostsByDate(userId, date);
    }
} 