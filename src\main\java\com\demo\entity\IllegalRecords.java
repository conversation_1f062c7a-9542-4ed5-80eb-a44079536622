package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demo.enums.*;
import com.demo.handler.IntegerArrayJSONTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 违法记录表
 */
@Data
@TableName(value = "`illegal_records`")
public class IllegalRecords {
    /**
     * UUID
     */
    @TableId(value = "UUID", type = IdType.INPUT)
    private String uuid;
    /**
     * 违法名称
     */
    @TableField(value = "`Illegal_name`")
    private String illegalName;

    /**
     * 违法类型（0正常车辆 1未佩戴头盔 2加装遮阳伞 3超员）
     */
    @TableField(value = "`illegal_type`")
    private TrafficEnum illegalType;
    /**
     * 上传设备编号
     */
    @TableField(value = "`equipment_number`")
    private String equipmentNumber;

    ///**
    // * 是否有车牌，有0无1
    // */
    //@TableField(value = "`involve_plate`")
    //private BrandEnum involvePlate;

    /**
     * 车牌颜色（0黄色，1白色，2绿色，3蓝色，4红色）
     */
    @TableField(value = "`plate_color`")
    private PlateColorEnum plateColor;

    /**
     * 车牌号
     */
    @TableField(value = "`plate_number`")
    private String plateNumber;

    /**
     * 车辆类型（0非机动车，1三轮车）
     */
    @TableField(value = "`vehicle_type`")
    private VehicleTypeEnum vehicleType;

    /**
     * 乘坐人数
     */
    @TableField(value = "`number_of_passengers`")
    private Integer numberOfPassengers;

    /**
     * 违法图片地址
     */
    @TableField(value = "`picture_url`")
    private String pictureUrl;

    /**
     * 是否进行劝导动作(0正常车辆不进行劝导，1已进行劝导，2未进行劝导)
     */
    @TableField(value = "`persuasive_behavior`")
    private PersuasionEnum persuasiveBehavior;

    /**
     * 处理状态(1未处理，3已下派劝导员，2劝导员已处理，0不需要处理)
     */
    @TableField(value = "`disposal_status`")
    private HandleEnum disposalStatus;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 点
     */
    @TableField(value = "`site`")
    private String site;
    /**
     * 经度
     */
    @TableField(value = "`longitude`")
    private String longitude;
    /**
     * 纬度
     */
    @TableField(value = "`latitude`")
    private String latitude;
    /**
     * 抓拍时间
     */
    @TableField(value = "`capture_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date captureTime;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 路口设备重启时间
     */
    @TableField(value = "`restart_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date restartTime;

    /**
     * 路口定义车辆ID
     */
    @TableField(value = "`vehicle_id`")
    private Integer vehicleId;

    /**
     * 路口摄像头名称
     */
    @TableField(value = "`camera_name`")
    private String cameraName;

    /**
     * 有效劝导视频地址
     */
    @TableField(value = "`video_url`")
    private String videoUrl;

    /**
     * 路口定义车辆IDTwo
     */
    @TableField(value = "`vehicle_id_two`")
    private Integer vehicleIdTwo;

    /**
     * 路口摄像头名称Two
     */
    @TableField(value = "`camera_name_two`")
    private String cameraNameTwo;

//    /**
//     * 是否有效劝导(0无效，1有效)
//     */
//    @TableField(value = "`effective`")
//    private Integer effective;


//    /**
//     * 替换后车辆类型
//     */
//    @TableField(value = "`actual_vehicle_type`")
//    private VehicleTypeEnum actualVehicleType;
//
//    /**
//     * 替换后车牌颜色
//     */
//    @TableField(value = "`actual_plate_color`")
//    private PlateColorEnum actualPlateColor;
//
//    /**
//     * 替换后车牌号
//     */
//    @TableField(value = "`actual_plate_number`")
//    private String actualPlateNumber;
//
//    /**
//     * 替换后违法类型
//     */
//    @TableField(value = "`actual_illegal_type`")
//    private TrafficEnum actualIllegalType;
//
//    /**
//     * 替换后乘坐人数
//     */
//    @TableField(value = "`actual_number_of_passengers`")
//    private Integer actualNumberOfPassengers;
//
//    /**
//     * 替换后图片地址
//     */
//    @TableField(value = "`actual_picture_url`")
//    private String actualPictureUrl;
//
//    /**
//     * 审核状态
//     */
//    @TableField(value = "`audit_status`")
//    private Integer auditStatus;
//    /**
//     * 是否修改
//     */
//    @TableField(value = "`whether_to_modify`")
//    private Integer whetherToModify;
    /**
     * 车牌图片地址
     */
    @TableField(value = "`numberplate_url`")
    private String numberplateUrl;
    @TableField(value = "`is_coming1`")
    private Boolean isComing1;
    @TableField(value = "`is_coming2`")
    private Boolean isComing2;
    /**
     * 在来向画面中打印的提示文本
     */
    @TableField(value = "`tips_text`")
    private String tipsText;
    /**
     * 左边画面的车辆与乘客合并区域（缩放后）
     */
    @TableField(value = "`illegal_zone1`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] illegalZone1;
    /**
     * 右边画面的车辆与乘客合并区域（缩放后)
     */
    @TableField(value = "`illegal_zone2`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] illegalZone2;


//    /**
//     * 更新后的车牌号
//     */
//    @TableField(value = "`update_plate_number`")
//    private String updatePlateNumber;

}