package com.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * GB28181和次服务器配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "gb28181")
public class Gb28181Config {
    
    /**
     * GB28181功能是否启用
     */
    private Boolean enabled = false;
    
    /**
     * SIP域
     */
    private String sipDomain = "3402000000";
    
    /**
     * SIP密码
     */
    private String sipPassword = "12345678";
    
    /**
     * 次服务器配置列表
     */
    private List<EdgeServer> edgeServers;
    
    @Data
    public static class EdgeServer {
        /**
         * 次服务器ID
         */
        private String id;
        
        /**
         * 次服务器名称
         */
        private String name;
        
        /**
         * 次服务器IP地址
         */
        private String ip;
        
        /**
         * SIP端口
         */
        private Integer sipPort = 5060;
        
        /**
         * 媒体端口范围
         */
        private String mediaPortRange = "10000-10100";
        
        /**
         * 摄像机网段
         */
        private String cameraNetwork;
        
        /**
         * 次服务器状态
         */
        private Boolean online = false;
        
        /**
         * 最后心跳时间
         */
        private Long lastHeartbeat;
    }
    
    /**
     * 根据摄像机IP获取对应的次服务器
     */
    public EdgeServer getEdgeServerByCameraIp(String cameraIp) {
        if (edgeServers == null) {
            return null;
        }
        
        for (EdgeServer server : edgeServers) {
            if (isIpInNetwork(cameraIp, server.getCameraNetwork())) {
                return server;
            }
        }
        return null;
    }
    
    /**
     * 根据服务器ID获取次服务器配置
     */
    public EdgeServer getEdgeServerById(String serverId) {
        if (edgeServers == null) {
            return null;
        }
        
        return edgeServers.stream()
                .filter(server -> server.getId().equals(serverId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 检查IP是否在指定网段内
     */
    private boolean isIpInNetwork(String ip, String network) {
        if (network == null || ip == null) {
            return false;
        }
        
        try {
            String[] networkParts = network.split("/");
            if (networkParts.length != 2) {
                return false;
            }
            
            String networkIp = networkParts[0];
            int prefixLength = Integer.parseInt(networkParts[1]);
            
            // 简单的网段匹配逻辑（可以使用更复杂的IP计算库）
            String[] ipParts = ip.split("\\.");
            String[] networkParts2 = networkIp.split("\\.");
            
            if (ipParts.length != 4 || networkParts2.length != 4) {
                return false;
            }
            
            // 根据前缀长度进行匹配
            int bytesToCheck = prefixLength / 8;
            int remainingBits = prefixLength % 8;
            
            for (int i = 0; i < bytesToCheck; i++) {
                if (!ipParts[i].equals(networkParts2[i])) {
                    return false;
                }
            }
            
            if (remainingBits > 0 && bytesToCheck < 4) {
                int ipByte = Integer.parseInt(ipParts[bytesToCheck]);
                int networkByte = Integer.parseInt(networkParts2[bytesToCheck]);
                int mask = (0xFF << (8 - remainingBits)) & 0xFF;
                
                if ((ipByte & mask) != (networkByte & mask)) {
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
