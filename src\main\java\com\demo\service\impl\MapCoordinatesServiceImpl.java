package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.entity.MapCoordinates;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.mapper.MapCoordinatesMapper;
import com.demo.service.MapCoordinatesService;


@Service
public class MapCoordinatesServiceImpl extends ServiceImpl<MapCoordinatesMapper, MapCoordinates> implements MapCoordinatesService{
    @Autowired
    private MapCoordinatesMapper mapCoordinatesMapper;

    @Override
    public SaResult getCoordinate(String city, String county) {
        QueryWrapper<MapCoordinates> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("city", city);
        queryWrapper.eq("county", county);
        MapCoordinates mapCoordinates = mapCoordinatesMapper.selectOne(queryWrapper);
        return SaResult.data(mapCoordinates);
    }
}
