<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.UserShiftGroupMapper">

    <!-- 根据用户ID查询用户班组信息 -->
    <select id="selectByUserId" resultType="com.demo.entity.UserShiftGroup">
        SELECT * FROM user_shift_group
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="selectAllCounselorsWithShiftGroup" resultType="java.lang.Integer">
        SELECT user_id 
        FROM user_shift_group 
        WHERE status = 1 
        AND group_id IS NOT NULL
        AND start_date &lt;= NOW()
        AND (end_date IS NULL OR end_date >= NOW())
    </select>

</mapper> 