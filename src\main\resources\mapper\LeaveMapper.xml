<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.LeaveMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Leave">
    <!--@mbg.generated-->
    <!--@Table `leave`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="leave_type" jdbcType="INTEGER" property="leaveType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="approved_by" jdbcType="INTEGER" property="approvedBy" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="approval_comment" jdbcType="VARCHAR" property="approvalComment" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, user_name, leave_type, start_time, end_time, reason, `status`, approved_by, 
    approved_time, approval_comment, cancel_reason, cancel_time, create_time, update_time
  </sql>
</mapper>