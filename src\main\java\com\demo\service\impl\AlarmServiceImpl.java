package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Alarm;
import com.demo.entity.Device;
import com.demo.entity.Users;
import com.demo.mapper.AlarmMapper;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.AlarmService;
import com.demo.utils.PhoneNotifyClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import static com.demo.config.RelatedConfigurations.relatedconfigurations;

@Service
public class AlarmServiceImpl extends ServiceImpl<AlarmMapper, Alarm> implements AlarmService {
    @Autowired
    private AlarmMapper alarmMapper;
    @Autowired
    UsersMapper usersMapper;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    PhoneNotifyClient phoneNotifyClient;
    @Override
    public SaResult selectAlarm(String alarmType, Date startTime, Date endTime, Integer curPage, Integer pageSize) {
        IPage<Alarm> page = new Page<>(curPage, pageSize);
        QueryWrapper<Alarm> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(alarmType), "alarm_type", alarmType);
        if (startTime != null && endTime != null) {
            queryWrapper.between("event_time", startTime, endTime);
        }
        queryWrapper.orderByDesc("event_time");
        IPage<Alarm> alarmIPage = alarmMapper.selectPage(page, queryWrapper);
        return SaResult.data(alarmIPage);
    }

    @Override
    public SaResult getLatestNews(String city, String county, String township, String hamlet, String site) {
        QueryWrapper<Alarm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
        queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
        queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);
        queryWrapper.ne("alarm_type", "手动报警");
        queryWrapper.orderByDesc("event_time");
        queryWrapper.last("LIMIT 10");
        List<Alarm> latestAlarms = alarmMapper.selectList(queryWrapper);
        return SaResult.data(latestAlarms);
    }

    @Override
    public SaResult manualAlarm() throws IOException {
        int loginIdAsInt = StpUtil.getLoginIdAsInt();
        //获取用户
        Users user = usersMapper.selectById(loginIdAsInt);
        if (relatedconfigurations.getPhone()){
            //todo 打电话劝导员一键报警（劝导员上级领导）
            phoneNotifyClient.sendPhoneNotify(user.getName() + "手动报警", "15388336114");
        }
        Alarm alarm = new Alarm();
        alarm.setAlarmType("手动报警");
        alarm.setContent(user.getName() + "手动报警");
        alarm.setDegree("严重");
        alarm.setTargetType(user.getName() + "手动报警,id:" + user.getUserId());
        alarm.setCity(user.getCity());
        alarm.setCounty(user.getCounty());
        alarm.setTownship(user.getTownship());
        alarm.setHamlet(user.getHamlet());
        alarm.setSite(user.getSite());
        alarm.setKnow(1);
        alarmMapper.insert(alarm);
       return SaResult.ok();
    }

    @Override
    public SaResult confirmAlarm(Integer id) {
        Alarm alarm = new Alarm();
        alarm.setId(id);
        alarm.setKnow(0);
        alarm.setUpdateTime(new Date());
        alarmMapper.updateById(alarm);
        return SaResult.ok();
    }

    @Override
    public SaResult getManualOperation() {
        QueryWrapper<Alarm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alarm_type", "手动报警");
        queryWrapper.eq("know", 1);
        queryWrapper.last("LIMIT 3"); // 限制结果数量为3条
        List<Alarm> manualOperation = alarmMapper.selectList(queryWrapper);
        return SaResult.data(manualOperation);
    }

    @Override
    public SaResult getIntersection(Integer id) {
        Alarm alarm = alarmMapper.selectById(id);
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(alarm.getCity()), "city", alarm.getCity());
        queryWrapper.eq(StringUtils.isNotBlank(alarm.getCounty()), "county", alarm.getCounty());
        queryWrapper.eq(StringUtils.isNotBlank(alarm.getTownship()), "township", alarm.getTownship());
        queryWrapper.eq(StringUtils.isNotBlank(alarm.getHamlet()), "hamlet", alarm.getHamlet());
        queryWrapper.eq(StringUtils.isNotBlank(alarm.getSite()), "site", alarm.getSite());
        queryWrapper.eq("device_type", "摄像机");
        queryWrapper.eq("state", 0);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        if (devices.size() > 0) {
            return SaResult.data(devices);
        }
        return SaResult.error( alarm.getCity()+ alarm.getCounty()+ alarm.getTownship()
                + alarm.getHamlet()+ alarm.getSite()+"人员报警,但设备离线");
    }

    @Override
    public SaResult getAlarmPrompt(String city, String county, String township, String hamlet, String site) {
        QueryWrapper<Alarm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
        queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
        queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);
        queryWrapper.eq("alarm_type", "手动报警");
        List<Alarm> alarms = alarmMapper.selectList(queryWrapper);
        // 按处理状态分组
        Map<String, List<Alarm>> groupedAlarms = new HashMap<>();
        groupedAlarms.put("handled", new ArrayList<>());     // 已处理
        groupedAlarms.put("unhandled", new ArrayList<>());   // 未处理
        // 分类处理
        for (Alarm alarm : alarms) {
            if (alarm.getKnow() != null && alarm.getKnow() == 0) {
                groupedAlarms.get("handled").add(alarm);
            } else {
                groupedAlarms.get("unhandled").add(alarm);
            }
        }
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("handled", groupedAlarms.get("handled"));      // 已处理的报警
        result.put("unhandled", groupedAlarms.get("unhandled")); // 未处理的报警
        
        return SaResult.data(result);
    }
}
