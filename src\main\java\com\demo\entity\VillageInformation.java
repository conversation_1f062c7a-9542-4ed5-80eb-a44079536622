package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@TableName("village_information")
public class VillageInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**ID*/
    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;
    /**市*/
    private String city;
    /**区县*/
    private String county;
    /**镇*/
    private String township;
    /**乡*/
    private String hamlet;
    /**点位*/
    private String site;
    /**辖区面积*/
    private String areaOfJurisdiction;
    /**户籍人口*/
    private String registeredPopulation;
    /**机动车数*/
    private Integer vehicles;
    /**驾驶员数*/
    private Integer drivers;
    /**运输企业数*/
    private Integer transportCompanies;
    /**非机动车数*/
    private Integer nonMotorVehicles;
    /**重点货运企业数*/
    private Integer keyFreightCompanies;
    /**国道公里数*/
    private Integer nationalRoads;
    /**省道公里数*/
    private Integer provincialRoads;
    /**县道公里数*/
    private Integer countyRoads;
    /**乡道公里数*/
    private Integer townshipRoads;
    /**村道公里数*/
    private Integer villageRoads;
    /**对外通道公里数*/
    private Integer externalRoads;
    /**类别*/
    private String category;
    /**创建时间*/
    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**更新时间*/
    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 分层级基础数据返回结构
     */
    @Data
    public static class HierarchicalDataResponse {
        private Integer code = 200;
        private String message = "success";
        private HierarchicalData data;
    }

    /**
     * 层级数据结构
     */
    @Data
    public static class HierarchicalData {
        // 当前级别的汇总数据
        private String name;                    // 区域名称
        private Integer totalVehicles;          // 总机动车数
        private Integer totalDrivers;           // 总驾驶员数
        private Integer totalTransportCompanies; // 总运输企业数
        private String area;                    // 面积（可选）
        private String population;              // 人口（可选）

        // 下级区域数据列表
        private List<RegionPoint> point;
    }

    /**
     * 区域点位数据
     */
    @Data
    public static class RegionPoint {
        private String name;                    // 区域名称
        private Integer vehicles;               // 机动车数
        private Integer drivers;                // 驾驶员数
        private Integer transportCompanies;     // 运输企业数
        private String area;                    // 面积（可选）
        private String population;              // 人口（可选）

        // 以下字段用于弹窗详情显示（可选）
        private Integer nonMotorVehicles;       // 非机动车数
        private Integer keyFreightCompanies;    // 重点货运企业数
        private RoadInfo roads;                 // 道路信息（可选）
    }

    /**
     * 道路信息
     */
    @Data
    public static class RoadInfo {
        private Integer national;               // 国道公里数
        private Integer provincial;             // 省道公里数
        private Integer county;                 // 县道公里数
        private Integer township;               // 乡道公里数
        private Integer village;                // 村道公里数
        private Integer external;               // 对外通道公里数
    }

    /**
     * 区域基础数据汇总（通用）
     */
    @Data
    public static class RegionTotalSummary {
        private Integer code = 200;
        private String message = "success";
        private RegionSummaryData data;
    }

    /**
     * 区域数据汇总（通用）
     */
    @Data
    public static class RegionSummaryData {
        private String regionName;              // 区域名称
        private String regionLevel;             // 区域级别（city/county/township/hamlet）
        private Integer totalVehicles;          // 总机动车数
        private Integer totalDrivers;           // 总驾驶员数
        private Integer totalTransportCompanies; // 总运输企业数
        private Integer totalNonMotorVehicles;  // 总非机动车数
        private Integer totalKeyFreightCompanies; // 总重点货运企业数
        private String totalArea;               // 总面积
        private String totalPopulation;         // 总人口
        private RoadInfo totalRoads;            // 总道路信息

        // 下级统计信息（根据当前级别动态显示）
        private Integer subRegionCount;         // 下级区域数量
        private String subRegionType;           // 下级区域类型（如：区县、镇、村、点位）
        private Integer recordCount;            // 记录总数
    }
}
