//package com.demo.entity;
//
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableField;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import java.util.Date;
//
//import com.demo.enums.AttendanceEnum;
//import lombok.Data;
//
///**
// * 考勤记录表
// */
//@Data
//@TableName(value = "`attendance_records`")
//public class AttendanceRecords {
//    /**
//     * 记录唯一标识符
//     */
//    @TableId(value = "id", type = IdType.AUTO)
//    private Integer id;
//
//    /**
//     * 员工ID，关联到users表
//     */
//    @TableField(value = "`user_id`")
//    private Integer userId;
//
//    /**
//     * 班次ID，关联到shifts表，用于标记当天应上的班次
//     */
//    @TableField(value = "`shift_id`")
//    private Integer shiftId;
//
//    /**
//     * 出勤日期
//     */
//    @TableField(value = "`date`")
//    private Date date;
//
//    /**
//     * 实际打卡上班时间
//     */
//    @TableField(value = "`check_in_time`")
//    private Date checkInTime;
//
//    /**
//     * 实际打卡下班时间
//     */
//    @TableField(value = "`check_out_time`")
//    private Date checkOutTime;
//
//    /**
//     * 出勤状态，默认为正常
//     */
//    @TableField(value = "`status`")
//    private AttendanceEnum status;
//
//    /**
//     * 备注或说明，用于记录特殊情况，如请假原因等
//     */
//    @TableField(value = "`note`")
//    private String note;
//
//    /**
//     * 创建时间，默认为当前时间戳
//     */
//    @TableField(value = "`create_time`")
//    private Date createTime;
//}