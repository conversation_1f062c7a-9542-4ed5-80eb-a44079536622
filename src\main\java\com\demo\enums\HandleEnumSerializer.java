package com.demo.enums;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class HandleEnumSerializer extends StdSerializer<HandleEnum> {

    public HandleEnumSerializer() {
        super(HandleEnum.class);
    }

    @Override
    public void serialize(HandleEnum value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("code", value.getCode());
        gen.writeStringField("desc", value.getDesc());
        gen.writeStringField("color", value.getColor()); // 添加颜色字段
        gen.writeEndObject();
    }
}
