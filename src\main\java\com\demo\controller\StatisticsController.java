package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.service.AccuratePersuasionService;
import com.demo.service.AlarmService;
import com.demo.service.DeviceService;
import com.demo.service.IllegalRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Date;

/**
 * 统计
 */
@RestController
@RequestMapping("/statistics")
public class StatisticsController {
    @Autowired
    private IllegalRecordsService illegalRecordsService;
    @Autowired
    DeviceService deviceService;
    @Autowired
    AccuratePersuasionService accuratePersuasionService;
    @Autowired
    AlarmService alarmService;

    /**
     * 今日违法数量
     */
    @GetMapping("/todayStatistics")
    public SaResult today() {
        return illegalRecordsService.todayStatistics();
    }

    /**
     * 设备总数
     */
    @GetMapping("/deviceCount")
    public SaResult deviceCount() {
        return deviceService.deviceCount();
    }

    /**
     * 设备在线率
     */
    @GetMapping("/deviceOnline")
    public SaResult deviceOnline() {
        return deviceService.deviceOnline();
    }

    /**
     * 处理率
     */
    @GetMapping("/processingRate")
    public SaResult processingRate() {
        return accuratePersuasionService.processingRate();
    }

    /**
     * 获取年内违法点位最多的前七个
     *
     * @param year
     * @return
     */
    @GetMapping("/topLocationsByYear")
    public SaResult topLocationsByYear(int year) {
        return illegalRecordsService.getTopLocationsByYear(year);
    }

    /**
     * 获取月内违法点位最多的前七个
     *
     * @param year
     * @return
     */
    @GetMapping("/topLocationsByMonth")
    public SaResult topLocationsByMonth(int year, int month) {
        return illegalRecordsService.getTopLocationsByMonth(year, month);
    }

    /**
     * 获取日内违法点位最多的前七个
     *
     * @param year
     * @return
     */
    @GetMapping("/topLocationsByDay")
    public SaResult topLocationsByDay(int year, int month, int day) {
        return illegalRecordsService.getTopLocationsByDay(year, month, day);
    }

    /**
     * 获取周内违法点位最多的前七个
     */
    @GetMapping("/topLocationsByWeek")
    public SaResult topLocationsByWeek(int year, int week) {
        return illegalRecordsService.getTopLocationsByWeek(year, week);
    }

    /**
     * 获取过去七天内违法点位最多的前七个
     */
    @GetMapping("/topLocationsByLastSevenDays")
    public SaResult topLocationsByLastSevenDays() {
        return illegalRecordsService.getTopLocationsByLastSevenDays();
    }
//    /**
//     * 获取按天统计的违法类型分布趋势
//     */
//    @GetMapping("/violationTrendsByDay")
//    public SaResult violationTrendsByDay(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
//        return illegalRecordsService.getViolationTrendsByDay(date, city, county, township, hamlet, site);
//    }
//    /**
//     * 获取按周统计的违法类型分布趋势
//     */
//    @GetMapping("/violationTrendsByWeek")
//    public SaResult violationTrendsByWeek(@RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date startTime,
//                                          @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date endTime) {
//        System.out.println(startTime);
//        System.out.println(endTime);
//        return illegalRecordsService.getViolationTrendsByWeek(startTime, endTime);
//    }
//
//    /**
//     * 获取按月统计的违法类型分布趋势
//     */
//    @GetMapping("/violationTrendsByMonth")
//    public SaResult violationTrendsByMonth(int year, int month) {
//        return illegalRecordsService.getViolationTrendsByMonth(year, month);
//    }
//
//    /**
//     * 获取按年统计的违法类型分布趋势
//     */
//    @GetMapping("/violationTrendsByYear")
//    public SaResult violationTrendsByYear(int year) {
//        return illegalRecordsService.getViolationTrendsByYear(year);
//    }

    /**
     * 获取按天统计的违法类型次数
     */
    @GetMapping("/violationCountsByDay")
    public SaResult violationCountsByDay(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
                                         @RequestParam(required = false) String city,
                                         @RequestParam(required = false) String county,
                                         @RequestParam(required = false) String township,
                                         @RequestParam(required = false) String hamlet,
                                         @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationCountsByDay(date,city, county, township, hamlet, site);
    }


    /**
     * 获取指定时间段内的违法类型次数
     */
    @GetMapping("/violationCountsByTimeRange")
    public SaResult violationCountsByTimeRange(
            @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date endTime,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationCountsByTimeRange(startTime, endTime,city, county, township, hamlet, site);
    }

    /**
     * 最新动态
     */
    @GetMapping("/latestNews")
    public SaResult latestNews(@RequestParam(required = false) String city,
                               @RequestParam(required = false) String county,
                               @RequestParam(required = false) String township,
                               @RequestParam(required = false) String hamlet,
                               @RequestParam(required = false) String site) {
        return alarmService.getLatestNews(city, county, township, hamlet, site);
    }
}
