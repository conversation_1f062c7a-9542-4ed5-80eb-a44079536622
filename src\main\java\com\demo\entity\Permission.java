package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 权限表
 * <AUTHOR>
 */
@Data
@TableName(value = "`permission`")
public class Permission {
    /**
     * id
     */
    @TableId(value = "permission_id", type = IdType.AUTO)
    private Integer permissionId;

    /**
     * 权限
     */
    @TableField(value = "`permission_code`")
    private String permissionCode;

    /**
     * 备注
     */
    @TableField(value = "`remarks`")
    private String remarks;

    /**
     * 所属区域
     */
    @TableField(value = "`plate`")
    private String plate;
}