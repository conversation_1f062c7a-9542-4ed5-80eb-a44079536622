package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 劝导相关的枚举
 * <AUTHOR>
 */
@Getter
public enum PersuasionEnum  implements BaseEnum{
//    NORMAL(0, "正常车辆不进行劝导","#00FF00"), //绿色
    PERSUADED(1, "已进行劝导-无效劝导","#00FF00"), //绿色
    NOT_PERSUADED(2, "未劝导","#FF0000" ), //红色
    EFFECTIVE_PERSUASION(3, "已进行劝导-有效劝导","#00FF00");

    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    private final String color;
    PersuasionEnum(int code, String desc, String color) {
        this.code = code;
        this.desc = desc;
        this.color = color;
    }

    public static PersuasionEnum of(int code) {
        for (PersuasionEnum persuasionEnum : values()) {
            if (persuasionEnum.getCode() == code) {
                return persuasionEnum;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static PersuasionEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }
}
