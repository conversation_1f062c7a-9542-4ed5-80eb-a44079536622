package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 处理状态枚举
 */
@Getter
public enum HandleEnum  implements BaseEnum{
    NOT_REQUIRED(0,"不需要处理","#00FF00"), // 绿色
    UNTREATED(1, "未处理","#FF0000" ), //红色
    TREATED(2, "已处理","#00FF00"), // 绿色
    SENT_DOWN(3, "已下发","#00FFFF"), //青色
    WRITE_OFF(4, "核销","#FF0000"); //红色

    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    private final String color;

    HandleEnum(int code, String desc, String color) {
        this.code = code;
        this.desc = desc;
        this.color = color;
    }

    public static HandleEnum of(int code) {
        for (HandleEnum handleEnum : values()) {
            if (handleEnum.getCode() == code) {
                return handleEnum;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static HandleEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }
}
