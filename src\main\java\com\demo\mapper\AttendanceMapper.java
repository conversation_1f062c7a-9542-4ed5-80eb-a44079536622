package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Attendance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface AttendanceMapper extends BaseMapper<Attendance> {
    List<Attendance> selectByScheduleId(@Param("scheduleId") Integer scheduleId);

    /**
     * 批量查询用户的最后一次打卡记录
     * @param userIds 用户ID列表
     * @return 用户最后一次打卡记录列表
     */
    List<Attendance> selectLastRecordBatch(@Param("userIds") List<Integer> userIds);

    /**
     * 根据排班ID查询考勤记录
     *
     * @param scheduleId 排班ID
     * @return 考勤记录
     */
    Attendance getByScheduleId(Integer scheduleId);

    /**
     * 按地区和日期查询考勤统计数据
     * @param city 市
     * @param county 县
     * @param township 镇
     * @param hamlet 村
     * @param site 点位
     * @param date 日期
     * @param groupByLevel 分组层级
     * @return 考勤统计数据
     */
    List<Map<String, Object>> getAttendanceStatsByArea(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("date") LocalDate date,
            @Param("groupByLevel") String groupByLevel
    );

    /**
     * 批量查询多日期考勤统计数据 - 用于月度/年度汇总优化
     * @param city 市
     * @param county 县
     * @param township 镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupByLevel 分组层级
     * @return 考勤统计数据
     */
    List<Map<String, Object>> getAttendanceStatsByAreaBatch(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("groupByLevel") String groupByLevel
    );
}