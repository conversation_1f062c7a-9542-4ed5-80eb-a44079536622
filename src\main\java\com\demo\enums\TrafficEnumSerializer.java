package com.demo.enums;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class TrafficEnumSerializer extends StdSerializer<TrafficEnum> {

    public TrafficEnumSerializer() {
        super(TrafficEnum.class);
    }

    @Override
    public void serialize(TrafficEnum value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("code", value.getCode());
        gen.writeStringField("desc", value.getDesc());
        gen.writeStringField("color", value.getColor()); // 添加颜色字段
        gen.writeEndObject();
    }
}
