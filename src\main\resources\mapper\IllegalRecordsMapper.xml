<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.IllegalRecordsMapper">
    <resultMap id="BaseResultMap" type="com.demo.entity.IllegalRecords">
        <!--@mbg.generated-->
        <!--@Table `illegal_records`-->
        <id column="UUID" jdbcType="VARCHAR" property="uuid"/>
        <result column="Illegal_name" jdbcType="VARCHAR" property="illegalName"/>
        <result column="illegal_type" jdbcType="INTEGER" property="illegalType"
                typeHandler="com.demo.enums.TrafficEnumTypeHandler"/>
        <!--    <result column="involve_plate" jdbcType="INTEGER" property="involvePlate" typeHandler="com.demo.enums.BrandEnumTypeHandler"/>-->
        <result column="plate_color" jdbcType="INTEGER" property="plateColor"
                typeHandler="com.demo.enums.PlateColorEnumTypeHandler"/>
        <result column="plate_number" jdbcType="VARCHAR" property="plateNumber"/>
        <result column="overload_situation" jdbcType="INTEGER" property="vehicleType"
                typeHandler="com.demo.enums.VehicleTypeEnumTypeHandler"/>
        <result column="number_of_passengers" jdbcType="INTEGER" property="numberOfPassengers"/>
        <result column="picture_url" jdbcType="LONGVARCHAR" property="pictureUrl"/>
        <result column="persuasive_behavior" jdbcType="INTEGER" property="persuasiveBehavior"
                typeHandler="com.demo.enums.PersuasionEnumTypeHandler"/>
        <!--    <result column="hand_movement" jdbcType="INTEGER" property="handMovement" typeHandler="com.demo.enums.AssignEnumTypeHandler"/>-->
        <result column="disposal_status" jdbcType="INTEGER" property="disposalStatus"
                typeHandler="com.demo.enums.HandleEnumTypeHandler"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="township" jdbcType="VARCHAR" property="township"/>
        <result column="hamlet" jdbcType="VARCHAR" property="hamlet"/>
        <result column="site" jdbcType="VARCHAR" property="site"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="capture_time" jdbcType="TIMESTAMP" property="captureTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="restart_time" jdbcType="TIMESTAMP" property="restartTime"/>
        <result column="vehicle_id" jdbcType="INTEGER" property="vehicleId"/>
        <result column="camera_name" jdbcType="VARCHAR" property="cameraName"/>
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
        <result column="vehicle_id_two" jdbcType="INTEGER" property="vehicleIdTwo"/>
        <result column="camera_name_two" jdbcType="VARCHAR" property="cameraNameTwo"/>
        <result column="equipment_number" jdbcType="VARCHAR" property="equipmentNumber"/>
        <result column="numberplate_url" jdbcType="VARCHAR" property="numberplateUrl"/>
<!--        <result column="update_plate_number" jdbcType="VARCHAR" property="updatePlateNumber"/>-->
<!--        <result column="actual_vehicle_type" jdbcType="INTEGER" property="actualVehicleType"-->
<!--                typeHandler="com.demo.enums.VehicleTypeEnumTypeHandler"/>-->
<!--        <result column="actual_plate_color" jdbcType="INTEGER" property="actualPlateColor"-->
<!--                typeHandler="com.demo.enums.PlateColorEnumTypeHandler"/>-->
<!--        <result column="actual_plate_number" jdbcType="VARCHAR" property="actualPlateNumber"/>-->
<!--        <result column="actual_illegal_type" jdbcType="INTEGER" property="actualIllegalType"-->
<!--                typeHandler="com.demo.enums.TrafficEnumTypeHandler"/>-->
<!--        <result column="actual_number_of_passengers" jdbcType="INTEGER" property="actualNumberOfPassengers"/>-->
<!--        <result column="actual_picture_url" jdbcType="VARCHAR" property="actualPictureUrl"/>-->
<!--        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>-->
<!--        <result column="whether_to_modify" jdbcType="INTEGER" property="whetherToModify"/>-->
        <result column="is_coming1" jdbcType="TINYINT" property="isComing1"/>
        <result column="is_coming2" jdbcType="TINYINT" property="isComing2"/>
        <result column="tips_text" jdbcType="VARCHAR" property="tipsText"/>
        <result column="illegal_zone1" jdbcType="VARCHAR" property="illegalZone1"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="illegal_zone2" jdbcType="VARCHAR" property="illegalZone2"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
    </resultMap>

    <resultMap id="ViolationTrendResultMap" type="com.demo.entity.ViolationTrend">
        <result column="illegal_type" property="illegalType" typeHandler="com.demo.enums.TrafficEnumTypeHandler"/>
        <result column="period" property="period"/>
        <result column="count" property="count"/>
    </resultMap>

    <resultMap id="ViolationCountResultMap" type="com.demo.entity.ViolationCount">
        <result column="illegal_type" property="illegalType" typeHandler="com.demo.enums.TrafficEnumTypeHandler"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 获取按年统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByYear" resultMap="ViolationTrendResultMap">
        SELECT illegal_type, MONTH(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type, MONTH(create_time)
        ORDER BY period
    </select>

    <!-- 获取按月统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByMonth" resultMap="ViolationTrendResultMap">
        SELECT illegal_type, DAY(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        GROUP BY illegal_type, DAY(create_time)
        ORDER BY period
    </select>

    <!-- 获取按周统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByWeek" resultMap="ViolationTrendResultMap">
        SELECT illegal_type,
        DATEDIFF(DATE(create_time), DATE(#{startTime})) as period,
        COUNT(*)                                        as count
        FROM illegal_records
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type,
        DATEDIFF(DATE(create_time), DATE(#{startTime}))
        ORDER BY DATEDIFF(DATE(create_time), DATE(#{startTime}))
    </select>

    <!-- 查询年内违法点位最多的前七个 -->
    <select id="selectTopLocationsByYear" resultType="map">
        SELECT CONCAT(city, ' ', county, ' ', township, ' ', hamlet, ' ', site) as location, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询月内违法点位最多的前七个 -->
    <select id="selectTopLocationsByMonth" resultType="map">
        SELECT CONCAT(city, ' ', county, ' ', township, ' ', hamlet, ' ', site) as location, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询日内违法点位最多的前七个 -->
    <select id="selectTopLocationsByDay" resultType="map">
        SELECT CONCAT(city, ' ', county, ' ', township, ' ', hamlet, ' ', site) as location, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        AND DAY(create_time) = #{day}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询周内违法点位最多的前七个 -->
    <select id="selectTopLocationsByWeek" resultType="map">
        SELECT CONCAT(city, ' ', county, ' ', township, ' ', hamlet, ' ', site) as location, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND WEEK(create_time, 1) = #{week}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询过去七天内违法点位最多的前七个 -->
    <select id="selectTopLocationsByLastSevenDays" resultType="map">
        SELECT CONCAT(city, ' ', county, ' ', township, ' ', hamlet, ' ', site) as location, COUNT(*) as count
        FROM illegal_records
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 获取按天统计的违法类型次数 -->
    <select id="selectViolationCountsByDay" resultMap="ViolationCountResultMap">
        SELECT illegal_type, COUNT(*) as count
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC
    </select>

    <!-- 获取按天统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByDay" resultMap="ViolationTrendResultMap">
        SELECT illegal_type, HOUR(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type, HOUR(create_time)
        ORDER BY period
    </select>

    <!-- 获取指定时间段内的违法类型次数 -->
    <select id="selectViolationCountsByTimeRange" resultMap="ViolationCountResultMap">
        SELECT illegal_type, COUNT(*) as count
        FROM illegal_records
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC
    </select>

    <select id="topLocationsByDay" resultType="java.util.Map">
        WITH all_locations AS (
        SELECT DISTINCT
        <choose>
            <when test="groupByLevel == 'county'">
                city, county
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet
            </when>
            <otherwise>
                city, county, township, hamlet, site
            </otherwise>
        </choose>
        FROM illegal_records
        <where>
            <choose>
                <when test="groupByLevel == 'county'">
                    AND county != '' AND county IS NOT NULL
                </when>
                <when test="groupByLevel == 'township'">
                    AND township != '' AND township IS NOT NULL
                </when>
                <when test="groupByLevel == 'hamlet'">
                    AND hamlet != '' AND hamlet IS NOT NULL
                </when>
                <otherwise>
                    AND site != '' AND site IS NOT NULL
                </otherwise>
            </choose>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        </where>
        ),
        data_counts AS (
        SELECT
        <choose>
            <when test="groupByLevel == 'county'">
                city, county,
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township,
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet,
            </when>
            <otherwise>
                city, county, township, hamlet, site,
            </otherwise>
        </choose>
        COUNT(*) AS count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        AND DAY(create_time) = #{day}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        <choose>
            <when test="groupByLevel == 'county'">
                city, county
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet
            </when>
            <otherwise>
                city, county, township, hamlet, site
            </otherwise>
        </choose>
        )
        SELECT
        <choose>
            <when test="groupByLevel == 'county'">
                CONCAT(l.county) AS location,
            </when>
            <when test="groupByLevel == 'township'">
                CONCAT(l.township) AS location,
            </when>
            <when test="groupByLevel == 'hamlet'">
                CONCAT(l.hamlet) AS location,
            </when>
            <otherwise>
                CONCAT(l.site) AS location,
            </otherwise>
        </choose>
        l.city,
        l.county,
        <if test="groupByLevel == 'township' or groupByLevel == 'hamlet' or groupByLevel == 'site'">
            l.township,
        </if>
        <if test="groupByLevel == 'hamlet' or groupByLevel == 'site'">
            l.hamlet,
        </if>
        <if test="groupByLevel == 'site'">
            l.site,
        </if>
        COALESCE(d.count, 0) AS count
        FROM all_locations l
        LEFT JOIN data_counts d ON
        <choose>
            <when test="groupByLevel == 'county'">
                l.city = d.city AND l.county = d.county
            </when>
            <when test="groupByLevel == 'township'">
                l.city = d.city AND l.county = d.county AND l.township = d.township
            </when>
            <when test="groupByLevel == 'hamlet'">
                l.city = d.city AND l.county = d.county AND l.township = d.township AND l.hamlet = d.hamlet
            </when>
            <otherwise>
                l.city = d.city AND l.county = d.county AND l.township = d.township AND l.hamlet = d.hamlet AND l.site = d.site
            </otherwise>
        </choose>
        ORDER BY count DESC
        LIMIT 10
    </select>

    <select id="topLocationsByMonth" resultType="java.util.Map">
        WITH all_locations AS (
        SELECT DISTINCT
        <choose>
            <when test="groupByLevel == 'county'">
                city, county
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet
            </when>
            <otherwise>
                city, county, township, hamlet, site
            </otherwise>
        </choose>
        FROM illegal_records
        <where>
            <choose>
                <when test="groupByLevel == 'county'">
                    AND county != '' AND county IS NOT NULL
                </when>
                <when test="groupByLevel == 'township'">
                    AND township != '' AND township IS NOT NULL
                </when>
                <when test="groupByLevel == 'hamlet'">
                    AND hamlet != '' AND hamlet IS NOT NULL
                </when>
                <otherwise>
                    AND site != '' AND site IS NOT NULL
                </otherwise>
            </choose>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        </where>
        ),
        data_counts AS (
        SELECT
        <choose>
            <when test="groupByLevel == 'county'">
                city, county,
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township,
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet,
            </when>
            <otherwise>
                city, county, township, hamlet, site,
            </otherwise>
        </choose>
        COUNT(*) AS count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        <choose>
            <when test="groupByLevel == 'county'">
                city, county
            </when>
            <when test="groupByLevel == 'township'">
                city, county, township
            </when>
            <when test="groupByLevel == 'hamlet'">
                city, county, township, hamlet
            </when>
            <otherwise>
                city, county, township, hamlet, site
            </otherwise>
        </choose>
        )
        SELECT
        <choose>
            <when test="groupByLevel == 'county'">
                CONCAT(l.county) AS location,
            </when>
            <when test="groupByLevel == 'township'">
                CONCAT(l.township) AS location,
            </when>
            <when test="groupByLevel == 'hamlet'">
                CONCAT(l.hamlet) AS location,
            </when>
            <otherwise>
                CONCAT(l.site) AS location,
            </otherwise>
        </choose>
        l.city,
        l.county,
        <if test="groupByLevel == 'township' or groupByLevel == 'hamlet' or groupByLevel == 'site'">
            l.township,
        </if>
        <if test="groupByLevel == 'hamlet' or groupByLevel == 'site'">
            l.hamlet,
        </if>
        <if test="groupByLevel == 'site'">
            l.site,
        </if>
        COALESCE(d.count, 0) AS count
        FROM all_locations l
        LEFT JOIN data_counts d ON
        <choose>
            <when test="groupByLevel == 'county'">
                l.city = d.city AND l.county = d.county
            </when>
            <when test="groupByLevel == 'township'">
                l.city = d.city AND l.county = d.county AND l.township = d.township
            </when>
            <when test="groupByLevel == 'hamlet'">
                l.city = d.city AND l.county = d.county AND l.township = d.township AND l.hamlet = d.hamlet
            </when>
            <otherwise>
                l.city = d.city AND l.county = d.county AND l.township = d.township AND l.hamlet = d.hamlet AND l.site = d.site
            </otherwise>
        </choose>
        ORDER BY count DESC
        LIMIT 10
    </select>

    <select id="selectDayStatistics" resultType="java.util.Map">
        SELECT h.hour,
        COUNT(ir.UUID) AS count
        FROM (SELECT 0 AS hour
        UNION ALL
        SELECT 1
        UNION ALL
        SELECT 2
        UNION ALL
        SELECT 3
        UNION ALL
        SELECT 4
        UNION ALL
        SELECT 5
        UNION ALL
        SELECT 6
        UNION ALL
        SELECT 7
        UNION ALL
        SELECT 8) h
        LEFT JOIN illegal_records ir ON HOUR(ir.create_time) = h.hour AND DATE(ir.create_time) = #{date}
        GROUP BY h.hour
        ORDER BY h.hour;
    </select>

    <select id="selectMonthStatistics" resultType="java.util.Map">
        SELECT DAY(d.date)                 AS day,
               COALESCE(COUNT(ir.UUID), 0) AS count
        FROM (SELECT DATE_ADD(DATE_FORMAT(CONCAT(YEAR(CURDATE()), '-', MONTH(CURDATE()), '-01'), '%Y-%m-%d'), INTERVAL n
                              DAY) AS date
              FROM (SELECT a.N + b.N * 10 AS n
                    FROM (SELECT 0 AS N
                          UNION ALL
                          SELECT 1
                          UNION ALL
                          SELECT 2
                          UNION ALL
                          SELECT 3
                          UNION ALL
                          SELECT 4
                          UNION ALL
                          SELECT 5
                          UNION ALL
                          SELECT 6
                          UNION ALL
                          SELECT 7
                          UNION ALL
                          SELECT 8) a,
                         (SELECT 0 AS N
                          UNION ALL
                          SELECT 1
                          UNION ALL
                          SELECT 2
                          UNION ALL
                          SELECT 3
                          UNION ALL
                          SELECT 4
                          UNION ALL
                          SELECT 5
                          UNION ALL
                          SELECT 6
                          UNION ALL
                          SELECT 7
                          UNION ALL
                          SELECT 8) b) numbers
              WHERE DATE_ADD(DATE_FORMAT(CONCAT(YEAR(CURDATE()), '-', MONTH(CURDATE()), '-01'), INTERVAL n
                  DAY) &lt;= LAST_DAY(CURDATE())) d
    LEFT JOIN illegal_records ir
              ON DATE (ir.create_time) = d.date
              GROUP BY DAY (d.date)
              ORDER BY DAY (d.date);
    </select>

    <select id="getTheSameDayViolationType" resultMap="ViolationCountResultMap">
        SELECT illegal_type, HOUR(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE DATE(create_time) = CURDATE()
        GROUP BY illegal_type, HOUR(create_time)
        ORDER BY period
    </select>

    <select id="getTheViolationTypeOfTheMonth" resultMap="ViolationCountResultMap">
        SELECT illegal_type,
               COUNT(*) AS count
        FROM illegal_records
        WHERE create_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
          AND create_time &lt; DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, '%Y-%m-01')
        GROUP BY YEAR(create_time), MONTH(create_time), illegal_type
        ORDER BY count DESC;
    </select>

    <select id="getPointDetailDataByDay" resultType="map">
        WITH traffic_types AS (
        -- 生成所有违法类型（除了正常车辆类型0）
        SELECT 1 as illegal_type
        UNION
        SELECT 2
        UNION
        SELECT 3
        UNION
        SELECT 5
        UNION
        SELECT 6
        UNION
        SELECT 7
        UNION
        SELECT 8),
        daily_stats AS (
        SELECT illegal_type,
        COUNT(*)                                                   AS total_violations,
        SUM(CASE WHEN disposal_status IN (2, 3) THEN 1 ELSE 0 END) AS dispatched_count,
        SUM(CASE WHEN disposal_status = 2 THEN 1 ELSE 0 END)       AS persuaded_count
        FROM illegal_records
        <where>
            <if test="year != null and year != ''">
                AND YEAR(create_time) = #{year}
            </if>
            <if test="month != null and month != ''">
                AND MONTH(create_time) = #{month}
            </if>
            <if test="day != null and day != ''">
                AND DAY(create_time) = #{day}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        </where>
        GROUP BY illegal_type
        )
        -- 关联所有违法类型和统计数据
        SELECT tt.illegal_type,
        COALESCE(ds.total_violations, 0) as total_violations,
        COALESCE(ds.dispatched_count, 0) as dispatched_count,
        COALESCE(ds.persuaded_count, 0)  as persuaded_count
        FROM traffic_types tt
        LEFT JOIN daily_stats ds ON tt.illegal_type = ds.illegal_type
        ORDER BY tt.illegal_type
    </select>

    <select id="getPointDetailDataByMonth" resultType="java.util.Map">
        SELECT illegal_type,
        COUNT(*)                                                   AS total_violations,
        SUM(CASE WHEN disposal_status IN (2, 3) THEN 1 ELSE 0 END) AS dispatched_count,
        SUM(CASE WHEN disposal_status = 2 THEN 1 ELSE 0 END)       AS persuaded_count
        FROM illegal_records
        <where>
            illegal_type in (1, 2, 3, 5, 6, 7, 8)
            <if test="year != null and year != ''">
                AND YEAR(create_time) = #{year}
            </if>
            <if test="month != null and month != ''">
                AND MONTH(create_time) = #{month}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        </where>
        GROUP BY illegal_type
    </select>

    <select id="getPointDetailDataByYear" resultType="java.util.Map">
        SELECT illegal_type,
        COUNT(*)                                                   AS total_violations,
        SUM(CASE WHEN disposal_status IN (2, 3) THEN 1 ELSE 0 END) AS dispatched_count,
        SUM(CASE WHEN disposal_status = 2 THEN 1 ELSE 0 END)       AS persuaded_count
        FROM illegal_records
        <where>
            illegal_type in (1, 2, 3, 5, 6, 7, 8)
            <if test="year != null and year != ''">
                AND YEAR(create_time) = #{year}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        </where>
        GROUP BY illegal_type
    </select>

    <!-- 获取工作时间内劝导统计(按地理层级汇总) -->
    <select id="getPersuasionStatsByWorkTime" resultType="map">
        WITH violation_stats AS (
            SELECT 
                ir.city,
                CASE 
                    WHEN #{county} IS NULL OR #{county} = '' THEN ir.county
                    WHEN #{township} IS NULL OR #{township} = '' THEN ir.township
                    WHEN #{hamlet} IS NULL OR #{hamlet} = '' THEN ir.hamlet
                    ELSE ir.site
                END as location_name,
                ir.county,
                ir.township,
                ir.hamlet,
                ir.site,
                ir.illegal_type,
                COUNT(DISTINCT ir.UUID) AS total_count,
                SUM(CASE WHEN ir.persuasive_behavior IN (1, 3) THEN 1 ELSE 0 END) AS persuaded_count,
                SUM(CASE WHEN ir.persuasive_behavior = 3 THEN 1 ELSE 0 END) AS effective_count
            FROM illegal_records ir
            WHERE ir.create_time BETWEEN #{startTime} AND #{endTime}
            AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
            AND ir.city = #{city}
            <if test="county != null and county != ''">
                AND ir.county = #{county}
                <if test="township != null and township != ''">
                    AND ir.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND ir.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND ir.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
            GROUP BY 
                ir.city,
                CASE 
                    WHEN #{county} IS NULL OR #{county} = '' THEN ir.county
                    WHEN #{township} IS NULL OR #{township} = '' THEN ir.township
                    WHEN #{hamlet} IS NULL OR #{hamlet} = '' THEN ir.hamlet
                    ELSE ir.site
                END,
                ir.illegal_type
        ),
        shift_info AS (
            SELECT 
                u.city,
                CASE 
                    WHEN #{county} IS NULL OR #{county} = '' THEN u.county
                    WHEN #{township} IS NULL OR #{township} = '' THEN u.township
                    WHEN #{hamlet} IS NULL OR #{hamlet} = '' THEN u.hamlet
                    ELSE u.site
                END as location_name,
                GROUP_CONCAT(
                    DISTINCT CONCAT(
                        sh.shift_name,
                        '(',
                        TIME_FORMAT(sh.start_time, '%H:%i'),
                        '-',
                        TIME_FORMAT(sh.end_time, '%H:%i'),
                        ')'
                    ) 
                    ORDER BY sh.start_time
                    SEPARATOR ', '
                ) AS shift_times
            FROM users u
            INNER JOIN schedule s ON s.user_id = u.user_id
            INNER JOIN shift sh ON s.shift_id = sh.id
            WHERE DATE(s.schedule_date) = DATE(#{startTime})
            AND u.city = #{city}
            <if test="county != null and county != ''">
                AND u.county = #{county}
                <if test="township != null and township != ''">
                    AND u.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND u.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND u.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
            GROUP BY 
                u.city,
                CASE 
                    WHEN #{county} IS NULL OR #{county} = '' THEN u.county
                    WHEN #{township} IS NULL OR #{township} = '' THEN u.township
                    WHEN #{hamlet} IS NULL OR #{hamlet} = '' THEN u.hamlet
                    ELSE u.site
                END
        )
        SELECT 
            vs.location_name,
            vs.city,
            vs.illegal_type,
            SUM(vs.total_count) as total_count,
            SUM(vs.persuaded_count) as persuaded_count,
            SUM(vs.effective_count) as effective_count,
            MAX(si.shift_times) as shift_times
        FROM violation_stats vs
        INNER JOIN shift_info si ON
            vs.city = si.city AND vs.location_name = si.location_name
        GROUP BY 
            vs.location_name,
            vs.city,
            vs.illegal_type
        ORDER BY vs.location_name, vs.illegal_type
    </select>

    <select id="getProselytizerShiftInfo" resultType="map">
        SELECT ps.city,
        ps.county,
        ps.township,
        ps.hamlet,
        ps.site,
        ps.start_time,
        ps.end_time,
        GROUP_CONCAT(DISTINCT ps.shift_name) as shift_names,
        GROUP_CONCAT(DISTINCT ps.user_name)  as user_names,
        COUNT(DISTINCT ps.user_id)           as user_count
        FROM (
        SELECT u.city,
        u.county,
        u.township,
        u.hamlet,
        u.site,
        u.user_id,
        u.name as user_name,
        sh.shift_name,
        sh.start_time,
        sh.end_time
        FROM users u
        INNER JOIN schedule s ON u.user_id = s.user_id
        INNER JOIN shift sh ON s.shift_id = sh.id
        WHERE u.state = 0
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        AND s.schedule_date BETWEEN #{startTime} AND #{endTime}
        ) ps
        GROUP BY ps.city,
        ps.county,
        ps.township,
        ps.hamlet,
        ps.site,
        ps.start_time
        ORDER BY ps.city, ps.county, ps.township, ps.hamlet, ps.site,
        ps.start_time
    </select>

    <!-- 获取违法地点列表 -->
    <select id="getViolationSites" resultType="map">
        SELECT ir.city,
        ir.county,
        ir.township,
        ir.hamlet,
        ir.site,
        COUNT(1)                                                    as total_violations,
        SUM(CASE WHEN ir.persuasive_behavior = 1 THEN 1 ELSE 0 END) as total_persuaded
        FROM illegal_records ir
        WHERE ir.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="city != null and city != ''">
            AND ir.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND ir.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND ir.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND ir.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND ir.site = #{site}
        </if>
        GROUP BY ir.city, ir.county, ir.township, ir.hamlet, ir.site
    </select>

    <!-- 获取排班信息 -->
    <select id="getSiteSchedule" resultType="map">
        SELECT DISTINCT u.name as user_name,
        u.user_id,
        s.shift_id,
        sh.shift_name,
        sh.start_time,
        sh.end_time
        FROM users u
        INNER JOIN schedule s ON u.user_id = s.user_id
        INNER JOIN shift sh ON s.shift_id = sh.id
        WHERE u.state = 0
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        AND DATE(s.schedule_date) = DATE(#{date})
        GROUP BY u.user_id, s.shift_id, sh.shift_name, sh.start_time, sh.end_time
        ORDER BY sh.start_time
    </select>

    <!-- 获取违法统计 -->
    <select id="getViolationStats" resultType="map">
        WITH daily_shifts AS (
        -- 获取每天的排班时间
        SELECT s.schedule_date,
        u.city,
        u.county,
        u.township,
        u.hamlet,
        u.site,
        sh.start_time,
        sh.end_time,
        sh.shift_name
        FROM users u
        INNER JOIN schedule s ON u.user_id = s.user_id
        INNER JOIN shift sh ON s.shift_id = sh.id
        WHERE u.state = 0
        AND DATE(s.schedule_date) = DATE(#{startTime}) -- 只查询当天的排班
        AND u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
            <if test="township != null and township != ''">
                AND u.township = #{township}
                <if test="hamlet != null and hamlet != ''">
                    AND u.hamlet = #{hamlet}
                    <if test="site != null and site != ''">
                        AND u.site = #{site}
                    </if>
                </if>
            </if>
        </if>
        ),
        daily_stats AS (
        -- 统计每天排班时间内的违法数据
        SELECT DATE(ir.create_time)                                        as stat_date,
        ir.illegal_type,
        COUNT(DISTINCT ir.UUID)                                     as total_count,
        SUM(CASE WHEN ir.persuasive_behavior = 1 THEN 1 ELSE 0 END) as persuaded_count
        FROM illegal_records ir
        INNER JOIN daily_shifts ds ON
        ds.city = ir.city
        AND ds.county = ir.county
        AND ds.township = ir.township
        AND ds.hamlet = ir.hamlet
        AND ds.site = ir.site
        AND DATE(ir.create_time) = DATE(ds.schedule_date)
        AND TIME(ir.create_time) BETWEEN ds.start_time AND ds.end_time
        WHERE ir.create_time BETWEEN #{startTime} AND #{endTime}
        AND ir.city = #{city}
        <choose>
            <when test="county != null and county != ''">
                AND ir.county = #{county}
                <choose>
                    <when test="township != null and township != ''">
                        AND ir.township = #{township}
                        <choose>
                            <when test="hamlet != null and hamlet != ''">
                                AND ir.hamlet = #{hamlet}
                                <choose>
                                    <when test="site != null and site != ''">
                                        AND ir.site = #{site}
                                    </when>
                                </choose>
                            </when>
                        </choose>
                    </when>
                </choose>
            </when>
        </choose>
        GROUP BY DATE(ir.create_time),
        ir.illegal_type
        ),
        shift_times AS (
        -- 获取每天的排班时间显示
        SELECT DATE(schedule_date) as shift_date,
        GROUP_CONCAT(
        DISTINCT CONCAT(
        shift_name,
        '(',
        TIME_FORMAT(start_time, '%H:%i'),
        '-',
        TIME_FORMAT(end_time, '%H:%i'),
        ')'
        )
        ORDER BY start_time
        SEPARATOR ', '
        ) as shift_times
        FROM daily_shifts
        GROUP BY DATE(schedule_date)
        )
        SELECT ds.stat_date                     as date,
        ds.illegal_type                  as type,
        ds.total_count                   as totalCount,
        ds.persuaded_count               as persuadedCount,
        IFNULL(st.shift_times, '无排班') as shiftTimes
        FROM daily_stats ds
        LEFT JOIN shift_times st ON ds.stat_date = st.shift_date
        ORDER BY ds.stat_date,
        ds.illegal_type
    </select>

    <!-- 获取班次时间范围 -->
    <select id="getShiftTimes" resultType="map">
        SELECT DISTINCT sh.shift_name,
                        sh.start_time,
                        sh.end_time
        FROM shift sh
        ORDER BY sh.start_time
    </select>

    <!-- 获取指定时间范围内的所有违法地点（按行政级别分组） -->
    <select id="getViolationLocations" resultType="map">
        SELECT DISTINCT ir.city
        <if test="city != null and city != ''">
            , ir.county
            <if test="county != null and county != ''">
                , ir.township
                <if test="township != null and township != ''">
                    , ir.hamlet
                    <if test="hamlet != null and hamlet != ''">
                        , ir.site
                    </if>
                </if>
            </if>
        </if>
        FROM illegal_records ir
        WHERE ir.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="city != null and city != ''">
            AND ir.city = #{city}
            <if test="county != null and county != ''">
                AND ir.county = #{county}
                <if test="township != null and township != ''">
                    AND ir.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND ir.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND ir.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
        </if>
        GROUP BY ir.city
        <if test="city != null and city != ''">
            , ir.county
            <if test="county != null and county != ''">
                , ir.township
                <if test="township != null and township != ''">
                    , ir.hamlet
                    <if test="hamlet != null and hamlet != ''">
                        , ir.site
                    </if>
                </if>
            </if>
        </if>
    </select>

    <!-- 获取指定日期的排班时间 -->
    <select id="getSiteSchedules" resultType="map">
        SELECT u.city,
        u.county,
        u.township,
        u.hamlet,
        u.site,
        sh.shift_name,
        sh.start_time,
        sh.end_time
        FROM users u
        INNER JOIN schedule s ON u.user_id = s.user_id
        INNER JOIN shift sh ON s.shift_id = sh.id
        WHERE u.state = 0
        AND DATE(s.schedule_date) = DATE(#{date})
        AND u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
            <if test="township != null and township != ''">
                AND u.township = #{township}
                <if test="hamlet != null and hamlet != ''">
                    AND u.hamlet = #{hamlet}
                    <if test="site != null and site != ''">
                        AND u.site = #{site}
                    </if>
                </if>
            </if>
        </if>
        group by sh.start_time,
        sh.end_time
    </select>

    <!-- 获取点位违法趋势统计(按天统计) -->
    <select id="getViolationTrendStats" resultType="map">
        WITH daily_shifts AS (
        -- 获取每天的排班时间
        SELECT s.schedule_date,
        u.city,
        u.county,
        u.township,
        u.hamlet,
        u.site,
        sh.shift_name,
        sh.start_time,
        sh.end_time
        FROM users u
        INNER JOIN schedule s ON u.user_id = s.user_id
        INNER JOIN shift sh ON s.shift_id = sh.id
        WHERE u.state = 0
        AND s.schedule_date BETWEEN #{startTime} AND #{endTime}
        AND u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
            <if test="township != null and township != ''">
                AND u.township = #{township}
                <if test="hamlet != null and hamlet != ''">
                    AND u.hamlet = #{hamlet}
                    <if test="site != null and site != ''">
                        AND u.site = #{site}
                    </if>
                </if>
            </if>
        </if>
        ),
        daily_stats AS (
        -- 统计每天排班时间内的违法数据
        SELECT DATE(ir.create_time)                                                                      as stat_date,
        ir.illegal_type,
        COUNT(DISTINCT ir.UUID)                                                                   as total_count,
        SUM(CASE
        WHEN ir.persuasive_behavior = 1 or ir.persuasive_behavior = 3 THEN 1
        ELSE 0 END)                                                                       as persuaded_count,
        SUM(CASE WHEN ir.persuasive_behavior = 3 THEN 1 ELSE 0 END)                               as effective_count
        FROM illegal_records ir
        INNER JOIN daily_shifts ds ON
        ds.city = ir.city
        AND (ds.county = ir.county OR ds.county IS NULL)
        AND (ds.township = ir.township OR ds.township IS NULL)
        AND (ds.hamlet = ir.hamlet OR ds.hamlet IS NULL)
        AND (ds.site = ir.site OR ds.site IS NULL)
        AND DATE(ir.create_time) = DATE(ds.schedule_date)
        AND TIME(ir.create_time) BETWEEN ds.start_time AND ds.end_time
        WHERE ir.create_time BETWEEN #{startTime} AND #{endTime}
        AND ir.city = #{city}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <choose>
            <when test="county != null and county != ''">
                AND ir.county = #{county}
                <choose>
                    <when test="township != null and township != ''">
                        AND ir.township = #{township}
                        <choose>
                            <when test="hamlet != null and hamlet != ''">
                                AND ir.hamlet = #{hamlet}
                                <choose>
                                    <when test="site != null and site != ''">
                                        AND ir.site = #{site}
                                    </when>
                                </choose>
                            </when>
                        </choose>
                    </when>
                </choose>
            </when>
        </choose>
        GROUP BY DATE(ir.create_time),
        ir.illegal_type
        ),
        shift_times AS (
        -- 获取每天的排班时间显示
        SELECT DATE(schedule_date) as shift_date,
        GROUP_CONCAT(
        DISTINCT CONCAT(
        shift_name,
        '(',
        TIME_FORMAT(start_time, '%H:%i'),
        '-',
        TIME_FORMAT(end_time, '%H:%i'),
        ')'
        )
        ORDER BY start_time
        SEPARATOR ', '
        ) as shift_times
        FROM daily_shifts
        GROUP BY DATE(schedule_date)
        )
        SELECT ds.stat_date                     as date,
        ds.illegal_type                  as type,
        ds.total_count                   as totalCount,
        ds.persuaded_count               as persuadedCount,
        ds.effective_count               as effectiveCount,
        IFNULL(st.shift_times, '无排班') as shiftTimes
        FROM daily_stats ds
        LEFT JOIN shift_times st ON ds.stat_date = st.shift_date
        ORDER BY ds.stat_date,
        ds.illegal_type
    </select>

    <!-- 获取点位违法趋势统计(按年统计，返回12个月数据) -->
    <select id="getLocationTrendStatsByYear" resultType="map">
        WITH monthly_shifts AS (
            -- 获取指定年份每月的排班时间
            SELECT
                MONTH(s.schedule_date) as month,
                u.city,
                u.county,
                u.township,
                u.hamlet,
                u.site,
                sh.shift_name,
                sh.start_time,
                sh.end_time
            FROM users u
            INNER JOIN schedule s ON u.user_id = s.user_id
            INNER JOIN shift sh ON s.shift_id = sh.id
            WHERE u.state = 0
            AND YEAR(s.schedule_date) = #{year}
            AND u.city = #{city}
            <if test="county != null and county != ''">
                AND u.county = #{county}
                <if test="township != null and township != ''">
                    AND u.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND u.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND u.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
        ),
        monthly_stats AS (
            -- 统计指定年份每月排班时间内的违法数据
            SELECT
                MONTH(ir.create_time) as stat_month,
                ir.illegal_type,
                COUNT(DISTINCT ir.UUID) as total_count,
                SUM(CASE
                    WHEN ir.persuasive_behavior = 1 or ir.persuasive_behavior = 3 THEN 1
                    ELSE 0 END) as persuaded_count,
                SUM(CASE WHEN ir.persuasive_behavior = 3 THEN 1 ELSE 0 END) as effective_count
            FROM illegal_records ir
            INNER JOIN monthly_shifts ms ON
                ms.city = ir.city
                AND (ms.county = ir.county OR ms.county IS NULL)
                AND (ms.township = ir.township OR ms.township IS NULL)
                AND (ms.hamlet = ir.hamlet OR ms.hamlet IS NULL)
                AND (ms.site = ir.site OR ms.site IS NULL)
                AND MONTH(ir.create_time) = ms.month
                AND TIME(ir.create_time) BETWEEN ms.start_time AND ms.end_time
            WHERE YEAR(ir.create_time) = #{year}
            AND ir.city = #{city}
            AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
            <choose>
                <when test="county != null and county != ''">
                    AND ir.county = #{county}
                    <choose>
                        <when test="township != null and township != ''">
                            AND ir.township = #{township}
                            <choose>
                                <when test="hamlet != null and hamlet != ''">
                                    AND ir.hamlet = #{hamlet}
                                    <choose>
                                        <when test="site != null and site != ''">
                                            AND ir.site = #{site}
                                        </when>
                                    </choose>
                                </when>
                            </choose>
                        </when>
                    </choose>
                </when>
            </choose>
            GROUP BY MONTH(ir.create_time), ir.illegal_type
        ),
        shift_times AS (
            -- 获取每月的排班时间显示
            SELECT
                month as shift_month,
                GROUP_CONCAT(
                    DISTINCT CONCAT(
                        shift_name,
                        '(',
                        TIME_FORMAT(start_time, '%H:%i'),
                        '-',
                        TIME_FORMAT(end_time, '%H:%i'),
                        ')'
                    )
                    ORDER BY start_time
                    SEPARATOR ', '
                ) as shift_times
            FROM monthly_shifts
            GROUP BY month
        )
        SELECT
            CONCAT(#{year}, '-', LPAD(ms.stat_month, 2, '0')) as date,
            ms.illegal_type as type,
            ms.total_count as totalCount,
            ms.persuaded_count as persuadedCount,
            ms.effective_count as effectiveCount,
            IFNULL(st.shift_times, '无排班') as shiftTimes
        FROM monthly_stats ms
        LEFT JOIN shift_times st ON ms.stat_month = st.shift_month
        ORDER BY ms.stat_month, ms.illegal_type
    </select>

    <!-- 获取24小时违法趋势统计 -->
    <select id="getHourlyViolationStats" resultType="map">
        SELECT HOUR(create_time) as hour,  <!-- 提取小时数 -->
        illegal_type as type,  <!-- 违法类型 -->
        COUNT(DISTINCT UUID) as total_count,  <!-- 违法总数 -->
        SUM(CASE WHEN persuasive_behavior = 1 THEN 1 ELSE 0 END) as persuaded_count,  <!-- 劝导成功数 -->
        SUM(CASE WHEN persuasive_behavior = 3 THEN 1 ELSE 0 END) as effective_count  <!-- 有效劝导数 -->
        FROM illegal_records
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        AND city = #{city}
        <!-- 根据传入的地理层级条件进行过滤 -->
        <if test="county != null and county != ''">
            AND county = #{county}
            <if test="township != null and township != ''">
                AND township = #{township}
                <if test="hamlet != null and hamlet != ''">
                    AND hamlet = #{hamlet}
                    <if test="site != null and site != ''">
                        AND site = #{site}
                    </if>
                </if>
            </if>
        </if>
        GROUP BY HOUR(create_time), illegal_type  <!-- 按小时和违法类型分组 -->
        ORDER BY hour, illegal_type  <!-- 按小时和违法类型排序 -->
    </select>

    <!-- 获取本月违法类型统计 -->
    <select id="getViolationTypeStatisticsByMonth" resultType="java.util.Map">
        SELECT illegal_type as type,
        COUNT(*)     as count
        FROM illegal_records
        WHERE create_time >= STR_TO_DATE(CONCAT(#{date}, '-01'), '%Y-%m-%d')
        AND create_time &lt; DATE_ADD(
        STR_TO_DATE(CONCAT(#{date}, '-01'), '%Y-%m-%d'),
        INTERVAL 1 MONTH
        )
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC
    </select>

    <select id="getTotalCountForToday" resultType="int">
        SELECT COUNT(*)
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <select id="getHandledCountForToday" resultType="int">
        SELECT COUNT(*)
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND disposal_status != 0
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <select id="getDayStatisticsWithDetails" resultType="java.util.Map">
        WITH hourly_stats AS (
        SELECT HOUR(create_time) as hour,
        illegal_type      as type,
        COUNT(*)          as type_count
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY HOUR(create_time), illegal_type
        ),
        total_stats AS (SELECT hour,
        SUM(type_count)     as total_count,
        CONCAT('[', GROUP_CONCAT(
        JSON_OBJECT('type', type, 'count', type_count)
        ), ']') as details
        FROM hourly_stats
        GROUP BY hour)
        SELECT hour,
        total_count as count,
        details
        FROM total_stats
        ORDER BY hour;
    </select>

    <select id="getTotalCountForMonth" resultType="int">
        SELECT COUNT(*)
        FROM illegal_records
        WHERE MONTH(create_time) = MONTH(#{date})
        AND YEAR(create_time) = YEAR(#{date})
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <select id="getHandledCountForMonth" resultType="int">
        SELECT COUNT(*)
        FROM illegal_records
        WHERE MONTH(create_time) = MONTH(#{date})
        AND YEAR(create_time) = YEAR(#{date})
        AND disposal_status != 0
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <select id="selectMonthStatisticsWithDetails" resultType="java.util.Map">
        WITH daily_stats AS (
        SELECT DAY(create_time) as day,
        illegal_type     as type,
        COUNT(*)         as type_count
        FROM illegal_records
        WHERE DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(#{date}, '%Y-%m')
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY DAY(create_time), illegal_type
        ),
        total_stats AS (SELECT day,
        SUM(type_count)     as total_count,
        CONCAT('[', GROUP_CONCAT(
        JSON_OBJECT('type', type, 'count', type_count)
        ), ']') as details
        FROM daily_stats
        GROUP BY day)
        SELECT day,
        total_count as count,
        details
        FROM total_stats
        ORDER BY day
    </select>

    <select id="getViolationTypeCountsForToday" resultType="java.util.Map">
        SELECT illegal_type AS type, COUNT(*) AS count
        FROM illegal_records
        WHERE DATE(create_time) = CURDATE()
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
    </select>

    <select id="getViolationTypeStatisticsToday" resultType="java.util.Map">
        SELECT illegal_type as type,
        COUNT(*)     as count
        FROM illegal_records
        WHERE DATE(create_time) = #{date}
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC;
    </select>

    <select id="getViolationTypeAnalysisByWeek" resultType="java.util.Map">
        SELECT illegal_type as type,
        COUNT(*)     as count
        FROM illegal_records
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        AND illegal_type in (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC
    </select>

    <select id="getViolationTypeAnalysisByYear" resultType="java.util.Map">
        SELECT illegal_type AS type,
        COUNT(*)     AS count
        FROM illegal_records
        WHERE  create_time >= DATE_FORMAT(CONCAT(#{year}, '-01-01'), '%Y-%m-%d')
        AND create_time &lt; DATE_ADD(DATE_FORMAT(CONCAT(#{year}, '-01-01'), '%Y-%m-%d'), INTERVAL 1 YEAR)
        AND illegal_type IN (1, 2, 3, 5, 6, 7, 8)
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        ORDER BY count DESC
    </select>

    <select id="getViolationTypeStatisticsByMap" resultType="java.util.Map">
        WITH traffic_types AS (
        -- 生成所有违法类型（除了正常车辆类型0）
        SELECT 1 as illegal_type
        UNION
        SELECT 2
        UNION
        SELECT 3
        UNION
        SELECT 5
        UNION
        SELECT 6
        UNION
        SELECT 7
        UNION
        SELECT 8),
        daily_stats AS (
        SELECT illegal_type,
        COUNT(*)                                                   AS total_violations,
        SUM(CASE WHEN disposal_status IN (2, 3) THEN 1 ELSE 0 END) AS dispatched_count,
        SUM(CASE WHEN disposal_status = 2 THEN 1 ELSE 0 END)       AS persuaded_count
        FROM illegal_records
        WHERE create_time BETWEEN #{startDate} AND #{endDate}
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY illegal_type
        )
        -- 关联所有违法类型和统计数据
        SELECT tt.illegal_type,
        COALESCE(ds.total_violations, 0) as total_violations,
        COALESCE(ds.dispatched_count, 0) as dispatched_count,
        COALESCE(ds.persuaded_count, 0)  as persuaded_count
        FROM traffic_types tt
        LEFT JOIN daily_stats ds ON tt.illegal_type = ds.illegal_type
        ORDER BY tt.illegal_type;
    </select>

    <select id="getPersuasionStatsInWorkingHours" resultType="java.util.Map">
        WITH traffic_types AS (
            -- 生成所有违法类型
            SELECT 1 as illegal_type
            UNION SELECT 2
            UNION SELECT 3
            UNION SELECT 5
            UNION SELECT 6
            UNION SELECT 7
            UNION SELECT 8
        ),
        location_stats AS (
            SELECT 
                ir.city,
                ir.county,
                ir.township,
                ir.hamlet,
                ir.site,
                tt.illegal_type as type,
                COUNT(ir.UUID) as total_count,
                SUM(CASE WHEN ir.disposal_status = 2 THEN 1 ELSE 0 END) as persuaded_count,
                SUM(CASE WHEN ir.disposal_status = 2 AND ir.persuasive_behavior = 1 THEN 1 ELSE 0 END) as effective_count
            FROM traffic_types tt
            LEFT JOIN illegal_records ir ON tt.illegal_type = ir.illegal_type
                AND ir.create_time BETWEEN #{startDate} AND #{endDate}
                AND (#{city} IS NULL OR ir.city = #{city})
                AND (#{county} IS NULL OR ir.county = #{county})
                AND (#{township} IS NULL OR ir.township = #{township})
                AND (#{hamlet} IS NULL OR ir.hamlet = #{hamlet})
                AND (#{site} IS NULL OR ir.site = #{site})
                AND EXISTS (
                    SELECT 1 
                    FROM (
                        <foreach item="shift" collection="workingHours" separator="UNION ALL">
                            SELECT 
                                #{shift.start_time} as start_time,
                                #{shift.end_time} as end_time
                        </foreach>
                    ) shifts
                    WHERE TIME(ir.create_time) BETWEEN TIME(shifts.start_time) AND TIME(shifts.end_time)
                )
            GROUP BY 
                <choose>
                    <when test="theNextLevel == 'county'">
                        ir.city, ir.county
                    </when>
                    <when test="theNextLevel == 'township'">
                        ir.city, ir.county, ir.township
                    </when>
                    <when test="theNextLevel == 'hamlet'">
                        ir.city, ir.county, ir.township, ir.hamlet
                    </when>
                    <otherwise>
                        ir.city, ir.county, ir.township, ir.hamlet, ir.site
                    </otherwise>
                </choose>,
                tt.illegal_type
            HAVING COUNT(ir.UUID) > 0
        )
        SELECT 
            city,
            CASE 
                WHEN #{theNextLevel} = 'county' THEN county
                WHEN #{theNextLevel} IN ('township', 'hamlet', 'site') THEN county
                ELSE NULL 
            END as county,
            CASE 
                WHEN #{theNextLevel} IN ('township', 'hamlet', 'site') THEN township
                ELSE NULL 
            END as township,
            CASE 
                WHEN #{theNextLevel} IN ('hamlet', 'site') THEN hamlet
                ELSE NULL 
            END as hamlet,
            CASE 
                WHEN #{theNextLevel} = 'site' THEN site
                ELSE NULL 
            END as site,
            CASE 
                WHEN #{theNextLevel} = 'county' THEN county
                WHEN #{theNextLevel} = 'township' THEN township
                WHEN #{theNextLevel} = 'hamlet' THEN hamlet
                ELSE site
            END as location_name,
            type,
            COALESCE(total_count, 0) as total_count,
            COALESCE(persuaded_count, 0) as persuaded_count,
            COALESCE(effective_count, 0) as effective_count,
            CONCAT(city, 
                COALESCE(county,''), 
                COALESCE(township,''), 
                COALESCE(hamlet,''), 
                COALESCE(site,'')
            ) as location
        FROM location_stats
        ORDER BY 
            location_name,
            type
    </select>

    <select id="exampleQueryWorkingHoursStatistics" resultType="java.util.Map">
        SELECT COUNT(*) as count,
<!--        SUM(CASE WHEN persuasive_behavior in (1,3) THEN 1 ELSE 0 END) as persuaded_count,-->
<!--        SUM(CASE WHEN persuasive_behavior = 3   THEN 1 ELSE 0 END) as effective_count,-->
                city, county, township, hamlet, site
        FROM illegal_records
        where create_time BETWEEN #{startTime} AND #{endTime}
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <select id="getPersuadedCountByWorkingHours" resultType="java.util.Map">
        SELECT
        COALESCE(SUM(CASE WHEN persuasive_behavior IN (1, 3) THEN 1 ELSE 0 END), 0) AS persuaded_count,
        COALESCE(SUM(CASE WHEN persuasive_behavior = 3 THEN 1 ELSE 0 END), 0) AS effective_count,
                city, county, township, hamlet, site
        FROM illegal_records
        where create_time BETWEEN #{startTime} AND #{endTime}
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
    </select>

    <!-- 统计指定车牌号在指定时间范围内的违法次数 -->
    <select id="countViolationsByPlateNumber" resultType="int">
        SELECT COUNT(*)
        FROM illegal_records
        WHERE plate_number = #{plateNumber}
          AND plate_number IS NOT NULL
          AND plate_number != ''
          AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

</mapper>