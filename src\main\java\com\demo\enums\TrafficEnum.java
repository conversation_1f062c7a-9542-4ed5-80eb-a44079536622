package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 交通相关的枚举
 *
 * <AUTHOR>
 */
@Getter
public enum TrafficEnum implements BaseEnum {
    NOT_WORN(1, "未佩戴头盔", null),
    MOTORCYCLE_OVERCROWDING(2, "加装遮阳伞", null),
    TRICYCLE(3, "超员", null),
    //SEVERE_OVERCROWDING(99, "其他", null),
    TRICYCLE_NOT_WORN(5, "超员+未佩戴头盔", null),
    TRICYCLE_MOTORCYCLE_OVERCROWDING(6, "超员+加装遮阳伞", null),
    NOT_WORN_MOTORCYCLE_OVERCROWDING(7, "未佩戴头盔+加装遮阳伞", null),
    TRICYCLE_NOT_WORN_MOTORCYCLE_OVERCROWDING(8, "超员+未佩戴头盔+加装遮阳伞", null);


    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    private final String color;

    TrafficEnum(int code, String desc, String color) {
        this.code = code;
        this.desc = desc;
        this.color = color;
    }

    public static TrafficEnum of(int code) {
        for (TrafficEnum traffic : values()) {
            if (traffic.getCode() == code) {
                return traffic;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static TrafficEnum getDesc(String desc) {
        for (TrafficEnum traffic : values()) {
            if (Objects.equals(traffic.getDesc(), desc)) {
                return traffic;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + desc + "]");
    }

    public static TrafficEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }

    public static TrafficEnum getByDesc(String desc) {
        for (TrafficEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        throw new IllegalArgumentException("未找到描述为 " + desc + " 的违法类型");
    }
}
