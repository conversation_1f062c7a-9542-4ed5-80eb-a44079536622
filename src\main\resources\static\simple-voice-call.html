<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易语音通话</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }

        .status {
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
            padding: 10px;
            background-color: #e0e0e0;
            border-radius: 4px;
        }

        .button-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        button.primary {
            background-color: #4CAF50;
            color: white;
        }

        button.primary:hover:not(:disabled) {
            background-color: #45a049;
        }

        button.danger {
            background-color: #f44336;
            color: white;
        }

        button.danger:hover:not(:disabled) {
            background-color: #d32f2f;
        }

        button.control {
            background-color: #2196F3;
            color: white;
        }

        button.control:hover:not(:disabled) {
            background-color: #0b7dda;
        }

        button.muted {
            background-color: #9e9e9e;
        }

        .logs {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f9f9f9;
        }

        .log {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }

        .log.info {
            color: #0277bd;
        }

        .log.success {
            color: #2e7d32;
        }

        .log.warning {
            color: #ef6c00;
        }

        .log.error {
            color: #c62828;
        }

        .log.debug {
            color: #757575;
        }

        .audio-level {
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }

        .audio-level-bar {
            height: 100%;
            width: 0%;
            background-color: #4CAF50;
            transition: width 0.1s;
        }
    </style>
    <script>
        // 立即执行的日志
        console.log('=== 页面开始加载 ===');
        
        window.addEventListener('load', function () {
            console.log('=== 页面加载完成 ===');
            
            // 从 URL 获取参数
            const urlParams = new URLSearchParams(window.location.search);
            const autoJoin = urlParams.get('autoJoin');
            
            if (autoJoin === 'true') {
                console.log('=== 检测到自动加入参数，准备加入房间 ===');
                setTimeout(function () {
                    const joinBtn = document.getElementById('join-room-btn');
                    if (joinBtn) {
                        console.log('=== 找到加入按钮，执行点击 ===');
                        joinBtn.click();
                        console.log('=== 已点击加入按钮 ===');

                        // 自动允许麦克风
                        navigator.mediaDevices.getUserMedia({ 
                            audio: {
                                echoCancellation: true,
                                noiseSuppression: true,
                                autoGainControl: true
                            },
                            video: false 
                        })
                        .then(function (stream) {
                            console.log('=== 麦克风权限已获取 ===');
                            localStream = stream;
                            // 自动点击开始通话按钮
                            setTimeout(() => {
                                const startBtn = document.getElementById('start-btn');
                                if (startBtn && !startBtn.disabled) {
                                    startBtn.click();
                                    console.log('=== 已点击开始通话按钮 ===');
                                }
                            }, 1000);
                        })
                        .catch(function (err) {
                            console.error('=== 获取麦克风失败 ===:', err);
                            // 尝试使用默认设备
                            navigator.mediaDevices.getUserMedia({ 
                                audio: true,
                                video: false 
                            })
                            .then(function(stream) {
                                console.log('=== 使用默认麦克风成功 ===');
                                localStream = stream;
                                setTimeout(() => {
                                    const startBtn = document.getElementById('start-btn');
                                    if (startBtn && !startBtn.disabled) {
                                        startBtn.click();
                                        console.log('=== 已点击开始通话按钮 ===');
                                    }
                                }, 1000);
                            })
                            .catch(function(err) {
                                console.error('=== 使用默认麦克风也失败 ===:', err);
                            });
                        });
                    } else {
                        console.error('=== 未找到加入按钮 ===');
                    }
                }, 2000);
            } else {
                console.log('=== 未检测到自动加入参数 ===');
            }
        });

        // 添加更多的事件监听
        window.addEventListener('DOMContentLoaded', function() {
            console.log('=== DOM内容加载完成 ===');
        });

        // 监听错误
        window.addEventListener('error', function(e) {
            console.error('=== 页面错误 ===:', e.message);
        });
    </script>
</head>

<body>
    <audio id="remote-audio" autoplay></audio>
    <div class="container">
        <h1>简易语音通话</h1>

        <div class="info">
            <div>设备ID: <span id="device-id"></span></div>
            <div>
                房间ID:
                <input type="text" id="room-id-input" value="test-room-621" style="width: 150px; margin-right: 10px;">
                <button id="join-room-btn" class="control" style="padding: 5px 10px; font-size: 12px;">加入房间</button>
            </div>
            <div style="margin-top: 10px;">
                麦克风:
                <select id="mic-select" style="width: 200px; margin-right: 10px;">
                    <option value="">加载中...</option>
                </select>
                <button id="refresh-devices-btn" class="control"
                    style="padding: 5px 10px; font-size: 12px;">刷新设备</button>
                <button id="test-mic-btn" class="control"
                    style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">测试麦克风</button>
            </div>
        </div>

        <div class="status" id="status">未连接</div>

        <div class="button-group">
            <button id="start-btn" class="primary">开始通话</button>
            <button id="end-btn" class="danger" disabled>结束通话</button>
            <button id="mute-btn" class="control" disabled>静音</button>
        </div>

        <div>
            <div>本地音频电平:</div>
            <div class="audio-level">
                <div class="audio-level-bar" id="local-audio-level"></div>
            </div>

            <div>远程音频电平:</div>
            <div class="audio-level">
                <div class="audio-level-bar" id="remote-audio-level"></div>
            </div>
        </div>

        <h3>日志</h3>
        <div class="logs" id="logs"></div>
    </div>

    <script>
        const config = {
            wsUrl: 'wss://countryside.yibindaoan.com/webrtc-ws',
            roomId: 'test-room-621',
            clientId: 'web-' + Math.random().toString(36).substr(2, 8),
            iceServers: [
                { 
                    urls: ['turn:***************:3478?transport=udp'],
                    username: 'zzb',
                    credential: '123456',
                    credentialType: 'password'
                },
                { 
                    urls: ['turn:***************:3478?transport=tcp'],
                    username: 'zzb',
                    credential: '123456',
                    credentialType: 'password'
                },
                {
                    urls: ['stun:***************:3478']
                }
            ]
        };

        let ws;
        let pc;
        let localStream;
        let remoteStream = null;
        let isConnected = false;
        let isMuted = false;
        let localAudioAnalyser = null;
        let remoteAudioAnalyser = null;
        let audioContext = null;
        let audioLevelInterval = null;
        let pendingCandidates = [];

        const remoteAudio = document.getElementById('remote-audio');
        const startBtn = document.getElementById('start-btn');
        const endBtn = document.getElementById('end-btn');
        const muteBtn = document.getElementById('mute-btn');
        const joinRoomBtn = document.getElementById('join-room-btn');
        const roomIdInput = document.getElementById('room-id-input');
        const statusEl = document.getElementById('status');
        const logsEl = document.getElementById('logs');
        const deviceIdEl = document.getElementById('device-id');
        const localAudioLevelEl = document.getElementById('local-audio-level');
        const remoteAudioLevelEl = document.getElementById('remote-audio-level');
        const micSelect = document.getElementById('mic-select');
        const refreshDevicesBtn = document.getElementById('refresh-devices-btn');
        const testMicBtn = document.getElementById('test-mic-btn');

        deviceIdEl.textContent = config.clientId;

        function log(message, type = 'info') {
            const logEl = document.createElement('div');
            logEl.className = `log ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent = `[${timestamp}] ${message}`;

            logsEl.appendChild(logEl);
            logsEl.scrollTop = logsEl.scrollHeight;

            console.log(`[${type}] ${message}`);
        }

        function updateStatus(status) {
            statusEl.textContent = status;
        }

        async function start() {
            try {
                startBtn.disabled = true;
                endBtn.disabled = false;
                muteBtn.disabled = false;

                await createPeerConnection();
                await getLocalStream();
                
                updateStatus('已连接');
                log('初始化完成');
            } catch (error) {
                log('初始化失败: ' + error.message);
                startBtn.disabled = false;
                endBtn.disabled = true;
                muteBtn.disabled = true;
            }
        }

        async function joinRoom() {
            try {
                const roomId = roomIdInput.value.trim();
                if (!roomId) {
                    log('请输入房间ID', 'error');
                    return;
                }

                config.roomId = roomId;
                log(`设置房间ID: ${config.roomId}`);

                // 先创建 PeerConnection 和获取本地流
                await createPeerConnection();
                await getLocalStream();

                // 然后发送加入房间消息
                sendMessage({
                    type: 'join',
                    roomId: config.roomId,
                    clientId: config.clientId
                });

                startBtn.disabled = true;
                endBtn.disabled = false;
                muteBtn.disabled = false;
                joinRoomBtn.disabled = true;
                roomIdInput.disabled = true;
                
                updateStatus('正在连接...');
                log('已加入房间，等待连接...');
            } catch (error) {
                log('加入房间失败: ' + error.message, 'error');
            }
        }

        async function createPeerConnection() {
            try {
                pc = new RTCPeerConnection({
                    iceServers: config.iceServers,
                    sdpSemantics: 'unified-plan',
                    bundlePolicy: 'max-bundle',
                    rtcpMuxPolicy: 'require'
                });

                pc.onicecandidate = event => {
                    if (event.candidate) {
                        sendMessage({
                            type: 'ice-candidate',
                            candidate: event.candidate
                        });
                    }
                };

                pc.ontrack = event => {
                    log('收到远程音频轨道');
                    remoteStream = event.streams[0];
                    
                    // 设置音频元素
                    remoteAudio.srcObject = remoteStream;
                    remoteAudio.autoplay = true;
                    remoteAudio.playsInline = true;  // 适配移动端
                    
                    // 设置音频分析器
                    setupAudioAnalyser(remoteStream, false);
                    
                    // 确保音频能播放
                    remoteAudio.play().catch(error => {
                        log('自动播放失败，可能需要用户交互: ' + error.message, 'warning');
                    });
                };

                pc.oniceconnectionstatechange = () => {
                    const state = pc.iceConnectionState;
                    log('ICE连接状态变化: ' + state);
                    
                    switch (state) {
                        case 'checking':
                            updateStatus('正在建立连接...');
                            break;
                        case 'connected':
                            updateStatus('已连接');
                            log('WebRTC连接已建立');
                            break;
                        case 'completed':
                            updateStatus('连接已完成');
                            break;
                        case 'failed':
                            updateStatus('连接失败');
                            log('WebRTC连接失败', 'error');
                            break;
                        case 'disconnected':
                            updateStatus('连接断开');
                            log('WebRTC连接断开', 'warning');
                            break;
                        case 'closed':
                            updateStatus('连接已关闭');
                            break;
                    }
                };

                pc.onconnectionstatechange = () => {
                    log('连接状态变化: ' + pc.connectionState);
                };

                pc.onsignalingstatechange = () => {
                    log('信令状态变化: ' + pc.signalingState);
                };

                log('PeerConnection 创建成功');
            } catch (err) {
                log('创建 PeerConnection 失败: ' + err.message, 'error');
                throw err;
            }
        }

        async function getLocalStream() {
            localStream = await navigator.mediaDevices.getUserMedia({
                audio: true,
                video: false
            });
            localStream.getTracks().forEach(track => {
                pc.addTrack(track, localStream);
            });
            log('获取本地音频流成功');
            setupAudioAnalyser(localStream, true);
        }

        async function handleOffer(message) {
            try {
                log('收到 offer');
                
                // 如果还没有创建 PeerConnection，先创建它
                if (!pc) {
                    await createPeerConnection();
                }
                
                // 如果还没有获取本地流，先获取
                if (!localStream) {
                    await getLocalStream();
                }
                
                await pc.setRemoteDescription(new RTCSessionDescription(message));
                
                // 处理之前暂存的 ICE 候选
                while (pendingCandidates.length > 0) {
                    const candidate = pendingCandidates.shift();
                    await pc.addIceCandidate(candidate);
                    log('处理暂存的 ICE 候选');
                }
                
                const answer = await pc.createAnswer();
                await pc.setLocalDescription(answer);
                
                sendMessage({
                    type: 'answer',
                    sdp: answer.sdp
                });
                
                updateStatus('已连接');
                log('已发送 answer');
            } catch (err) {
                log('处理 offer 失败: ' + err.message, 'error');
            }
        }

        async function handleAnswer(message) {
            try {
                log('处理 answer');
                await pc.setRemoteDescription(new RTCSessionDescription(message));
            } catch (error) {
                log('处理 answer 失败: ' + error.message);
            }
        }

        async function handleIceCandidate(message) {
            try {
                if (!message.candidate) return;
                
                const candidate = new RTCIceCandidate(message.candidate);
                
                // 如果还没有创建 PeerConnection，先创建它
                if (!pc) {
                    await createPeerConnection();
                    pendingCandidates.push(candidate);
                    log('暂存 ICE 候选');
                    return;
                }
                
                // 如果还没有设置远程描述，暂存候选
                if (!pc.remoteDescription) {
                    pendingCandidates.push(candidate);
                    log('暂存 ICE 候选');
                    return;
                }
                
                await pc.addIceCandidate(candidate);
                log('添加 ICE 候选成功');
            } catch (err) {
                log('添加 ICE 候选失败: ' + err.message, 'error');
            }
        }

        function initWebSocket() {
            // 添加重连相关变量
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;
            const reconnectDelay = 3000; // 3秒
            let reconnectTimeout;
            let heartbeatInterval;
            let heartbeatTimeout;

            function connect() {
                try {
                    log('正在连接WebSocket服务器...');
                    ws = new WebSocket(config.wsUrl);
                    setupWebSocketHandlers();
                } catch (err) {
                    log(`WebSocket连接创建失败: ${err.message}`, 'error');
                    scheduleReconnect();
                }
            }

            function setupWebSocketHandlers() {
                ws.onopen = () => {
                    log('WebSocket连接已建立', 'success');
                    reconnectAttempts = 0; // 重置重连次数
                    
                    // 启动心跳检测
                    startHeartbeat();
                    
                    // 如果URL中有autoJoin参数,自动加入房间
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.get('autoJoin') === 'true') {
                        setTimeout(() => {
                            const joinBtn = document.getElementById('join-room-btn');
                            if (joinBtn && !joinBtn.disabled) {
                                joinBtn.click();
                            }
                        }, 1000);
                    }
                };

                ws.onclose = (event) => {
                    log(`WebSocket连接已关闭 (code: ${event.code})`, 'warning');
                    stopHeartbeat();
                    scheduleReconnect();
                };

                ws.onerror = (error) => {
                    log('WebSocket发生错误', 'error');
                    // WebSocket发生错误时不主动关闭,让onclose处理重连
                };

                ws.onmessage = async (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        log(`收到消息: ${message.type}`);

                        // 处理心跳响应
                        if (message.type === 'heartbeat') {
                            // 清除超时计时器
                            if (heartbeatTimeout) {
                                clearTimeout(heartbeatTimeout);
                                heartbeatTimeout = null;
                            }
                            log('收到心跳响应', 'debug');
                            return;
                        }

                        switch (message.type) {
                            case 'join':
                                // 当收到其他客户端的加入消息时，如果我们已经准备好了，就发送 offer
                                if (message.clientId !== config.clientId && pc && localStream) {
                                    log(`新用户加入房间: ${message.clientId}`);
                                    await createAndSendOffer();
                                }
                                break;

                            case 'peer-joined':
                                // 兼容旧消息类型
                                if (message.clientId !== config.clientId && pc && localStream) {
                                    log(`新用户加入房间: ${message.clientId}`);
                                    await createAndSendOffer();
                                }
                                break;

                            case 'offer':
                                await handleOffer(message);
                                break;

                            case 'answer':
                                await handleAnswer(message);
                                break;

                            case 'ice-candidate':
                                await handleIceCandidate(message);
                                break;

                            case 'peer-left':
                                log(`用户离开房间: ${message.clientId}`);
                                break;

                            case 'room-status':
                                // 服务器主动推送房间状态时更新用户列表
                                if (message.clients) {
                                    updateClientList(message.clients);
                                }
                                break;

                            default:
                                log(`未知消息类型: ${message.type}`, 'warning');
                        }
                    } catch (error) {
                        log(`处理消息失败: ${error.message}`, 'error');
                    }
                };
            }

            function startHeartbeat() {
                // 清理可能存在的旧心跳
                stopHeartbeat();
                
                // 每20秒发送一次心跳
                heartbeatInterval = setInterval(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        sendMessage({
                            type: 'heartbeat',
                            timestamp: Date.now()
                        });
                        
                        // 设置心跳超时检测
                        heartbeatTimeout = setTimeout(() => {
                            log('心跳超时,准备重连', 'warning');
                            ws.close();
                        }, 5000); // 5秒内没收到响应就重连
                    }
                }, 20000);
            }

            function stopHeartbeat() {
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                }
                if (heartbeatTimeout) {
                    clearTimeout(heartbeatTimeout);
                    heartbeatTimeout = null;
                }
            }

            function scheduleReconnect() {
                if (reconnectTimeout) {
                    clearTimeout(reconnectTimeout);
                }

                if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    log(`准备第 ${reconnectAttempts} 次重连...`, 'info');
                    reconnectTimeout = setTimeout(() => {
                        connect();
                    }, reconnectDelay);
                } else {
                    log('达到最大重连次数,请刷新页面重试', 'error');
                    updateStatus('连接失败');
                }
            }

            // 初始连接
            connect();
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接,消息发送失败', 'error');
                return false;
            }

            try {
                message.roomId = config.roomId;
                message.clientId = config.clientId;
                ws.send(JSON.stringify(message));
                
                // 如果是心跳消息,使用debug级别记录
                if (message.type === 'heartbeat') {
                    log('发送心跳', 'debug');
                } else {
                    log(`发送消息: ${message.type}`);
                }
                return true;
            } catch (err) {
                log(`消息发送失败: ${err.message}`, 'error');
                return false;
            }
        }

        function setupAudioAnalyser(stream, isLocal) {
            try {
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                const analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;

                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);

                if (isLocal) {
                    localAudioAnalyser = analyser;
                } else {
                    remoteAudioAnalyser = analyser;
                }

                // 如果还没有启动音频电平监测，启动它
                if (!audioLevelInterval) {
                    audioLevelInterval = setInterval(updateAudioLevels, 100);
                    log('音频电平监测已启动');
                }

                log(`设置${isLocal ? '本地' : '远程'}音频分析器成功`);
            } catch (err) {
                log(`设置音频分析器失败: ${err.message}`);
            }
        }

        function stopAudioLevelMonitoring() {
            if (audioLevelInterval) {
                clearInterval(audioLevelInterval);
                audioLevelInterval = null;
            }

            if (audioContext) {
                try {
                    audioContext.close();
                } catch (err) {
                    // 忽略错误
                }
                audioContext = null;
            }

            localAudioAnalyser = null;
            remoteAudioAnalyser = null;

            localAudioLevelEl.style.width = '0%';
            remoteAudioLevelEl.style.width = '0%';
        }

        function updateAudioLevels() {
            if (localAudioAnalyser) {
                const dataArray = new Uint8Array(localAudioAnalyser.frequencyBinCount);
                localAudioAnalyser.getByteFrequencyData(dataArray);

                const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
                const level = Math.min(100, Math.max(0, average * 2));

                localAudioLevelEl.style.width = level + '%';
            }

            if (remoteAudioAnalyser) {
                const dataArray = new Uint8Array(remoteAudioAnalyser.frequencyBinCount);
                remoteAudioAnalyser.getByteFrequencyData(dataArray);

                const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
                const level = Math.min(100, Math.max(0, average * 2));

                remoteAudioLevelEl.style.width = level + '%';
            }
        }

        async function endCall() {
            try {
                log('结束通话');
                endBtn.disabled = true;
                muteBtn.disabled = true;

                cleanup();
                updateStatus('未连接');
                startBtn.disabled = false;
                log('通话已结束');
            } catch (err) {
                log(`结束通话失败: ${err.message}`);
                endBtn.disabled = false;
            }
        }

        function toggleMute() {
            if (!localStream) return;

            isMuted = !isMuted;

            localStream.getAudioTracks().forEach(track => {
                track.enabled = !isMuted;
            });

            muteBtn.textContent = isMuted ? '取消静音' : '静音';
            log(`麦克风已${isMuted ? '静音' : '取消静音'}`);
        }

        function setupEventListeners() {
            startBtn.addEventListener('click', start);
            endBtn.addEventListener('click', () => {
                leaveRoom();
            });
            muteBtn.addEventListener('click', toggleMute);
            joinRoomBtn.addEventListener('click', joinRoom);
            
            roomIdInput.addEventListener('keyup', (event) => {
                if (event.key === 'Enter') joinRoom();
            });
            
            window.addEventListener('beforeunload', () => {
                leaveRoom();
            });
            
            navigator.mediaDevices.addEventListener('devicechange', () => {
                log('检测到设备变化，重新加载设备列表');
                loadAudioDevices();
            });
        }

        window.addEventListener('DOMContentLoaded', async () => {
            try {
                startBtn.disabled = true;
                await loadAudioDevices();
                await initWebSocket();
                
                // 从 URL 中获取房间号参数
                const urlParams = new URLSearchParams(window.location.search);
                const roomIdFromUrl = urlParams.get('roomId');
                
                if (roomIdFromUrl) {
                    // 设置房间号输入框的值
                    roomIdInput.value = roomIdFromUrl;
                    log(`从URL获取到房间号: ${roomIdFromUrl}`);
                    
                    // 如果需要自动加入房间，可以自动点击加入按钮
                    if (urlParams.get('autoJoin') === 'true') {
                        log('准备自动加入房间...');
                        // 延迟一小段时间等待 WebSocket 连接建立
                        setTimeout(() => {
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                joinRoomBtn.click();
                            } else {
                                log('WebSocket未连接，无法自动加入房间', 'warning');
                            }
                        }, 1000);
                    }
                }
            } catch (err) {
                log(`初始化失败: ${err.message}`, 'error');
                joinRoomBtn.disabled = true;
            }
        });

        setupEventListeners();

        async function loadAudioDevices() {
            try {
                micSelect.innerHTML = '<option value="">加载中...</option>';

                try {
                    const tempStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    tempStream.getTracks().forEach(track => track.stop());
                } catch (err) {
                    log('获取麦克风权限失败，无法列出设备名称', 'warning');
                }

                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');

                if (audioInputs.length === 0) {
                    micSelect.innerHTML = '<option value="">未检测到麦克风</option>';
                    log('未检测到麦克风设备', 'warning');
                    return;
                }

                micSelect.innerHTML = '';

                const defaultOption = document.createElement('option');
                defaultOption.value = 'default';
                defaultOption.textContent = '默认麦克风';
                micSelect.appendChild(defaultOption);

                audioInputs.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.textContent = device.label || `麦克风 ${device.deviceId.slice(0, 5)}...`;
                    micSelect.appendChild(option);
                });

                log(`已加载 ${audioInputs.length} 个麦克风设备`, 'success');
            } catch (err) {
                log(`加载音频设备失败: ${err.message}`, 'error');
                micSelect.innerHTML = '<option value="">加载失败</option>';
            }
        }

        async function testMicrophone() {
            try {
                log('测试麦克风...');

                const deviceId = micSelect.value;

                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    log('浏览器不支持麦克风访问', 'error');
                    return;
                }

                const constraints = {
                    audio: deviceId === 'default' ? true : { deviceId: { exact: deviceId } },
                    video: false
                };

                const stream = await navigator.mediaDevices.getUserMedia(constraints);

                log('麦克风测试成功！');
                log(`正在使用麦克风: ${micSelect.options[micSelect.selectedIndex].text}`);

                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                const analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;

                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);

                const testInterval = setInterval(() => {
                    const dataArray = new Uint8Array(analyser.frequencyBinCount);
                    analyser.getByteFrequencyData(dataArray);

                    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
                    const level = Math.min(100, Math.max(0, average * 2));

                    localAudioLevelEl.style.width = level + '%';
                }, 100);

                setTimeout(() => {
                    clearInterval(testInterval);
                    stream.getTracks().forEach(track => track.stop());
                    localAudioLevelEl.style.width = '0%';
                    log('麦克风测试完成');
                }, 5000);

            } catch (err) {
                if (err.name === 'NotAllowedError') {
                    log('麦克风访问被拒绝。请在浏览器设置中允许麦克风访问', 'error');
                } else if (err.name === 'NotFoundError') {
                    log('未找到麦克风设备。请确保麦克风已连接', 'error');
                } else if (err.name === 'OverconstrainedError') {
                    log('无法使用选中的麦克风，请选择其他麦克风', 'error');
                } else {
                    log(`麦克风测试失败: ${err.name} - ${err.message}`, 'error');
                }
            }
        }

        function cleanup() {
            if (audioLevelInterval) {
                clearInterval(audioLevelInterval);
                audioLevelInterval = null;
            }

            if (audioContext) {
                audioContext.close().catch(console.error);
                audioContext = null;
            }

            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }

            if (pc) {
                pc.close();
                pc = null;
            }

            localAudioAnalyser = null;
            remoteAudioAnalyser = null;
            isConnected = false;
            isMuted = false;
        }

        async function createAndSendOffer() {
            try {
                log('创建并发送 offer...');
                const offer = await pc.createOffer({
                    offerToReceiveAudio: true,
                    voiceActivityDetection: true
                });
                
                log('设置本地描述...');
                await pc.setLocalDescription(offer);
                
                log('发送 offer...');
                sendMessage({
                    type: 'offer',
                    sdp: offer.sdp
                });
            } catch (error) {
                log('创建 offer 失败: ' + error.message, 'error');
            }
        }

        function leaveRoom() {
            // 发送离开消息
            if (ws && ws.readyState === WebSocket.OPEN) {
                sendMessage({
                    type: 'leave',
                    roomId: config.roomId,
                    clientId: config.clientId
                });
            }

            // 使用现有的清理函数
            cleanup();

            // 重置 UI 状态
            joinRoomBtn.disabled = false;
            roomIdInput.disabled = false;
            startBtn.disabled = true;
            endBtn.disabled = true;
            muteBtn.disabled = true;
            updateStatus('未连接');
            
            log('已离开房间');
        }

        function updateClientList(clients) {
            const usersList = document.getElementById('online-users-list');
            if (!usersList) return;

            usersList.innerHTML = '';
            clients.forEach(clientId => {
                const li = document.createElement('li');
                li.textContent = clientId;
                if (clientId === config.clientId) {
                    li.classList.add('self');
                    li.textContent += ' (我)';
                }
                usersList.appendChild(li);
            });

            log(`当前在线用户数: ${clients.length}`);
        }
    </script>
</body>

</html>