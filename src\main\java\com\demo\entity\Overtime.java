package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 加班记录表
 */
@Data
@TableName(value = "overtime")
public class Overtime {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 用户姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 加班时长（分钟）
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 所属市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 所属县
     */
    @TableField(value = "county")
    private String county;

    /**
     * 所属镇
     */
    @TableField(value = "township")
    private String township;

    /**
     * 所属村
     */
    @TableField(value = "hamlet")
    private String hamlet;

    /**
     * 所属点位
     */
    @TableField(value = "site")
    private String site;

    /**
     * 设备号
     */
    @TableField(value = "equipment_number")
    private String equipmentNumber;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}