package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.Shift;

import java.util.List;

public interface ShiftService extends IService<Shift> {
    // 获取指定班次组的班次
    List<Shift> getShiftsByGroup(Integer groupId);
    
    void addShiftToGroup(Integer shiftId, Integer groupId);
    
    void removeShiftFromGroup(Integer shiftId, Integer groupId);

    SaResult selectPage(IPage<Shift> pageRequest);
}