package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demo.config.Log;
import com.demo.config.ShiftOperationException;
import com.demo.entity.DTO.ShiftGroupCreationRequest;
import com.demo.entity.DTO.ShiftGroupDTO;
import com.demo.entity.Schedule;
import com.demo.entity.ShiftGroup;
import com.demo.enums.BusinessType;
import com.demo.service.ScheduleService;
import com.demo.service.ShiftGroupService;
import com.demo.service.ShiftService;
import com.demo.service.UserShiftGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 班组
 */
@RestController
@RequestMapping("/shift/group")
public class ShiftGroupController {

    @Autowired
    private ShiftGroupService shiftGroupService;

    @Autowired
    private ShiftService shiftService;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    UserShiftGroupService userShiftGroupService;

    /**
     * 查询班组
     *
     * @return 启用状态的班次组列表及其班次信息
     */
    @SaCheckPermission("/schedule-management/shift/group/list")
    @Log(title = "查询班组", businessType = BusinessType.SELECT)
    @GetMapping("/list")
    public SaResult list(@RequestParam(defaultValue = "1") int page,
                         @RequestParam(defaultValue = "10") int size) {
        Page<ShiftGroup> pageRequest = new Page<>(page, size);
        IPage<ShiftGroupDTO> resultPage = shiftGroupService.getActiveGroupsWithShifts(pageRequest);
        return SaResult.data(resultPage);
    }

    /**
     * 获取所有班组
     */
    @GetMapping("/listAll")
    public SaResult listAll() {
        List<ShiftGroup> shiftGroups = shiftGroupService.list();
        return SaResult.data(shiftGroups);
    }

    /**
     * 删除班组
     */
    @SaCheckPermission("/schedule-management/shift/group/Id")
    @Log(title = "删除班组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public SaResult delete(@PathVariable Integer id) {
        try {
            // 检查是否有用户的排班依赖于该班组
            List<Integer> userIds = userShiftGroupService.getGroupUsers(id);
            if (!userIds.isEmpty()) {
                return SaResult.error("无法删除班组，因为有用户依赖于该班组");
            }

            // 删除班组关联的班次
            List<Integer> shiftIds = shiftGroupService.getShiftIdsByGroupId(id);
            for (Integer shiftId : shiftIds) {
                shiftService.removeShiftFromGroup(shiftId, id);
            }

            // 删除班组
            boolean removed = shiftGroupService.removeById(id);
            if (removed) {
                return SaResult.ok("班组及其关联的班次已删除");
            } else {
                return SaResult.error("删除班组失败");
            }
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 新增班组
     *
     * @param request 班组创建请求
     * @return 创建结果
     */
    @SaCheckPermission("/schedule-management/shift/group/createWithShifts")
    @Log(title = "新增班组", businessType = BusinessType.INSERT)
    @PostMapping("/createWithShifts")
    @Transactional
    public SaResult createGroupWithShifts(@RequestBody ShiftGroupCreationRequest request) {
        try {
            // 创建班次组
            ShiftGroup group = new ShiftGroup();
            group.setGroupName(request.getGroupName());
            group.setWorkPattern(request.getWorkPattern());
            group.setDescription(request.getDescription());
            //group.setStatus(request.getStatus());
            shiftGroupService.createGroup(group);

            // 绑定班次
            for (Integer shiftId : request.getShiftIds()) {
                shiftService.addShiftToGroup(shiftId, group.getId());
            }

            return SaResult.ok("班组创建成功并已绑定班次");
        } catch (ShiftOperationException e) {
            // 返回详细的错误信息给前端
            return SaResult.error(e.getMessage());
        } catch (RuntimeException e) {
            // 返回一般错误信息并确保事务回滚
            throw new RuntimeException("创建班组失败: " + e.getMessage(), e);
        }
    }

    /**
     * 修改班组
     */

    @SaCheckPermission("/schedule-management/shift/group/update")
    @Log(title = "修改班组", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public SaResult updateShiftGroup(@RequestBody ShiftGroupCreationRequest request) {
        try {
            // 获取班次组中的班次ID
            List<Integer> oldShiftIds = shiftGroupService.getShiftIdsByGroupId(request.getId());

            // 删除旧的班次关联
            for (Integer shiftId : oldShiftIds) {
                shiftService.removeShiftFromGroup(shiftId, request.getId());
            }

            // 删除旧排班
            List<Integer> userIds = userShiftGroupService.getGroupUsers(request.getId());
            for (Integer userId : userIds) {
                scheduleService.remove(new LambdaQueryWrapper<Schedule>()
                        .eq(Schedule::getUserId, userId)
                        .ge(Schedule::getScheduleDate, DateUtil.today()));
            }

            // 更新班次组信息
            ShiftGroup group = new ShiftGroup();
            group.setId(request.getId());
            group.setGroupName(request.getGroupName());
            group.setWorkPattern(request.getWorkPattern());
            group.setDescription(request.getDescription());
            //group.setStatus(request.getStatus());
            shiftGroupService.updateById(group);

            // 绑定新的班次
            for (Integer shiftId : request.getShiftIds()) {
                shiftService.addShiftToGroup(shiftId, request.getId());
            }

            // 重新生成排班
            for (Integer userId : userIds) {
                scheduleService.generateSchedule(userId, request.getId(), LocalDateTime.now(), 30);
            }

            return SaResult.ok("班次组已更新并重新生成排班");
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }
} 