package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.Attendance;
import com.demo.entity.Device;
import com.demo.entity.Schedule;
import com.demo.entity.Shift;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AttendanceService extends IService<Attendance> {
    // 员工打卡（签到/签退）
    Attendance clock(Integer userId);
    
    // 获取员工考勤记录
    List<Attendance> getEmployeeAttendance(Integer userId, LocalDateTime startDate, LocalDateTime endDate);
    
    // 获取考勤统计
    Map<String, Object> getAttendanceStats(Integer userId, LocalDateTime startDate, LocalDateTime endDate);
    
    // 根据排班ID获取考勤记录
    Attendance getByScheduleId(Integer scheduleId);


    // 检查用户当天是否有签到记录
    boolean hasCheckInToday(Integer userId);

    // 获取用户的最后一次打卡记录
    Attendance getLastRecord(Integer userId);

    // 获取员工考勤记录(通过姓名)
    List<Attendance> getEmployeeAttendance(String userName, LocalDateTime startDate, LocalDateTime endDate);

    void recordAttendance(Integer userId, Integer type, LocalDateTime now, Integer scheduleId, MultipartFile[] image, Schedule currentSchedule, Shift currentShift);

    SaResult updateLeavePost(Device device);

}