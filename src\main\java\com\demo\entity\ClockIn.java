package com.demo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 上班打卡
 * <AUTHOR>
 */
@Data
public class ClockIn {
    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockInTime;
    /**
     * 必须传（凭证） 9999
     */
    private Integer token;

    /**
     * 设备编号
     */
    private String equipmentNumber;
}
