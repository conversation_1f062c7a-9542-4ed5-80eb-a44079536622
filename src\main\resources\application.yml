spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  profiles:
    active: prod
#    active: dev

# 推流控制配置
stream:
  # 推流总开关，true时允许所有设备推流，false时按观看者逻辑
  tuiliu: false
  # 允许推流的时间段（支持精确时间格式）
  allowed-time:
    # 开始时间（支持格式：7 或 07:00:00）
    start: "07:00:00"
    # 结束时间（支持格式：19 或 19:00:00）
    end: "19:00:00"

# GB28181和次服务器配置
gb28181:
  enabled: true
  sip-domain: "3402000000"
  sip-password: "12345678"
  # 次服务器配置
  edge-servers:
    - id: "edge-server-01"
      name: "次服务器1"
      ip: "*************"
      sip-port: 5060
      media-port-range: "10000-10100"
      camera-network: "************/24"
    - id: "edge-server-02"
      name: "次服务器2"
      ip: "*************"
      sip-port: 5060
      media-port-range: "10000-10100"
      camera-network: "************/24"
    # 可以继续添加其他次服务器...

# 使用 mybatis-plus 的配置
#mybatis-plus:
#  type-aliases-package: com.demo.entity
#  configuration:
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    db-config:
#      id-type: auto
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
