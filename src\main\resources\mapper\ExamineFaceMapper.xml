<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.ExamineFaceMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.ExamineFace">
    <!--@mbg.generated-->
    <!--@Table `examine_face`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="equipmentNumber" jdbcType="VARCHAR" property="equipmentnumber" />
    <result column="examine_url" jdbcType="VARCHAR" property="examineUrl" />
    <result column="examine" jdbcType="VARCHAR" property="examine" />
    <result column="binding_userId" jdbcType="INTEGER" property="bindingUserid" />
    <result column="binding_userName" jdbcType="VARCHAR" property="bindingUsername" />
    <result column="reviewer_id" jdbcType="INTEGER" property="reviewerId" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="township" jdbcType="VARCHAR" property="township" />
    <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="reviewer_time" jdbcType="TIMESTAMP" property="reviewerTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `equipmentNumber`, `examine_url`, `examine`, `binding_userId`, `binding_userName`, 
    `reviewer_id`, `reviewer`, `city`, `county`, `township`, `hamlet`, `site`, `create_time`, 
    `reviewer_time`
  </sql>
</mapper>