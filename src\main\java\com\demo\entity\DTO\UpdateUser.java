package com.demo.entity.DTO;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class UpdateUser {
    /**
     * id
     */
    private Integer userId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 市
     */
    private String city;

    /**
     * 县（区）
     */
    private String county;

    /**
     * 乡村（镇）
     */
    private String township;

    /**
     * 村
     */
    private String hamlet;

    /**
     * 点位
     */
    private String site;
}
