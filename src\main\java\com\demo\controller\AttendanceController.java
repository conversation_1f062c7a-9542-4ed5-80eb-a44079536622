package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.config.Log;
import com.demo.entity.Attendance;
import com.demo.entity.Device;
import com.demo.entity.Schedule;
import com.demo.entity.Shift;
import com.demo.enums.AttendanceEnum;
import com.demo.enums.BusinessType;
import com.demo.mapper.DeviceMapper;
import com.demo.service.*;
import com.demo.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.time.temporal.ChronoUnit;

import static com.demo.config.RelatedConfigurations.relatedconfigurations;
import com.demo.entity.Overtime;
import com.demo.entity.Users;
import com.demo.service.OvertimeService;
import com.demo.service.UsersService;

/**
 * 员工考勤
 */
@RestController
@RequestMapping("/attendance")
@Slf4j
public class AttendanceController {

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private ShiftService shiftService;
    @Autowired
    AttendanceScheduleService attendanceScheduleService;
    @Autowired
    ExamineFaceService examineFaceService;
    @Autowired
    LeavePostService leavePostService;
    @Autowired
    LeaveService leaveService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    UsersService usersService;

    ///**
    // * 获取员工考勤记录
    // *
    // * @param userId    员工ID
    // * @param startDate 开始日期
    // * @param endDate   结束日期
    // */
    //@GetMapping("/employee/{userId}")
    //public SaResult getAttendance(
    //        @PathVariable Integer userId,
    //        @RequestParam LocalDateTime startDate,
    //        @RequestParam LocalDateTime endDate) {
    //    return SaResult.data(attendanceService.getEmployeeAttendance(userId, startDate, endDate));
    //}

    /**
     * 记录打卡
     */
    @PostMapping("/record")
    @Transactional
    @SaIgnore
    public SaResult recordAttendance(
            @RequestParam(value = "image", required = false) MultipartFile[] image,
            @RequestParam(value = "token", required = false) String token,
            @RequestParam(value = "equipmentNumber", required = false) String equipmentNumber,
            @RequestParam(value = "userId", required = false) Integer userId) {
        if (!"9999".equals(token)) {
            System.out.println("token:----" + token);
            return SaResult.error("token错误");
        }
        // 查找设备信息
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        if (devices == null || devices.isEmpty()) {
            return SaResult.error("设备不存在");
        }
        Device device = devices.get(0);
        // 构建点位唯一标识，使用下划线作为分隔符
        String locationKey = String.format("%s_%s_%s_%s_%s",
                device.getCity(),
                device.getCounty(),
                device.getTownship(),
                device.getHamlet(),
                device.getSite());
        // 获取当前时间判断是上午还是下午
        String timeSlot = LocalTime.now().getHour() < 12 ? "am" : "pm";
        // 保存点位状态到Redis，区分上午和下午
        String redisKey = "site_status:" + locationKey + ":" + timeSlot;
        redisUtils.setEx(redisKey, "在岗", relatedconfigurations.getOffDutyTime(), TimeUnit.MINUTES);
        LocalDateTime now = LocalDateTime.now();
        if (userId == 0) {
            log.info("人脸识别未通过，接入图片审核");
            attendanceService.updateLeavePost(device);
            // 人脸识别未通过，记录专职劝导员加班

            overtimeService.recordOvertimeForFulltimeStaff(device, now);
            // 接入图片审核
            return examineFaceService.submitExamineFace(equipmentNumber, image);
        }
        try {
            // 获取当天的所有排班
            List<Schedule> schedules = scheduleService.getTodaySchedules(userId, now.toLocalDate());
            
            // 如果没有排班，记录为加班
            if (schedules.isEmpty()) {
                // 获取用户信息
                Users user = usersService.getById(userId);
                if (user != null) {
                    // 记录加班
                    Overtime overtime = overtimeService.recordOvertime(userId, user.getName(), device, now);
                    log.info("用户[{}]非排班时间打卡，记录加班", user.getName());
                    return SaResult.data("记录加班成功");
                } else {
                    log.warn("用户ID[{}]不存在", userId);
                    return SaResult.error("用户不存在");
                }
            }
            
            // 确定当前班次
            Schedule currentSchedule = null;
            Shift currentShift = null;
            for (Schedule schedule : schedules) {
                Shift shift = shiftService.getById(schedule.getShiftId());
                if (shift == null) {
                    continue;
                }
                LocalDateTime scheduleStart = schedule.getScheduleDate().with(shift.getStartTime());
                LocalDateTime scheduleEnd = schedule.getScheduleDate().with(shift.getEndTime());
                if (now.isAfter(scheduleStart.minusMinutes(30)) && now.isBefore(scheduleEnd.plusMinutes(10))) {
                    currentSchedule = schedule;
                    currentShift = shift;
                    break;
                }
            }
            
            // 如果当前时间不在任何排班时间段内，记录为加班
            if (currentSchedule == null) {
                // 获取用户信息
                Users user = usersService.getById(userId);
                if (user != null) {
                    // 记录加班
                    Overtime overtime = overtimeService.recordOvertime(userId, user.getName(), device, now);
                    log.info("用户[{}]非排班时间打卡，记录加班", user.getName());
                    return SaResult.data("记录加班成功");
                } else {
                    log.warn("用户ID[{}]不存在", userId);
                    return SaResult.error("用户不存在");
                }
            }
            
            // 判断是签到还是签退
            Integer type;
            LocalDateTime scheduleStart = currentSchedule.getScheduleDate().with(currentShift.getStartTime());
            LocalDateTime scheduleEnd = currentSchedule.getScheduleDate().with(currentShift.getEndTime());

            // 获取当前班次的打卡记录
            Attendance existingRecord = attendanceService.getByScheduleId(currentSchedule.getId());

            if (existingRecord == null) {
                // 如果没有打卡记录，检查是否在班次时间内签到
                if (now.isBefore(scheduleStart.minusMinutes(30)) || now.isAfter(scheduleEnd.plusMinutes(10))) {
                    throw new RuntimeException("签到必须在班次时间范围内");  //请在规定时间范围内打卡（上班前30分钟至下班后30分钟）
                }

                type = AttendanceEnum.NORMAL.getCode(); // 正常签到
            } else {
                // 如果已经有打卡记录
                if (existingRecord.getCheckOutTime() == null) {
                    // 判断当前时间是否已过下班时间
                    if (now.isAfter(scheduleEnd)) {
                        // 在下班时间之后，允许签退
                        type = 7; // 正常签退
                    } else {
                        // 在下班时间之前，视为重复签到
                        type = AttendanceEnum.REPEAT_CHECK_IN.getCode();
                    }
                } else {
                    // 如果已经有签退时间，判断是重复签到还是重复签退
                    if (now.isBefore(scheduleEnd)) {
                        type = AttendanceEnum.REPEAT_CHECK_IN.getCode(); // 5
                    } else {
                        type = AttendanceEnum.REPEAT_CHECK_OUT.getCode(); // 6
                    }
                }
            }
            // 记录打卡
            if (type == AttendanceEnum.NORMAL.getCode() || type == AttendanceEnum.REPEAT_CHECK_IN.getCode()) {
                // 签到或重复签到
                attendanceService.recordAttendance(userId, type, now, currentSchedule.getId(), image, currentSchedule, currentShift);
                log.info(type == AttendanceEnum.NORMAL.getCode() ? "签到成功" : "重复签到");
                return SaResult.data(type == AttendanceEnum.NORMAL.getCode() ? "签到成功" : null);
            } else {
                // 签退或重复签退
                // 如果有未结束的脱岗记录，结束它
                leavePostService.endLeavePost(userId, currentSchedule.getId(), now);
                attendanceService.recordAttendance(userId, type, now, currentSchedule.getId(), image, currentSchedule, currentShift);
                
                // 如果是正常签退且当前时间已经超过下班时间超过10分钟，记录加班
                if (type == 7 && now.isAfter(scheduleEnd.plusMinutes(10))) {
                    // 计算加班时长（分钟）
                    long overtimeMinutes = ChronoUnit.MINUTES.between(scheduleEnd, now);
                    log.info("用户ID[{}]下班后打卡，超出正常下班时间{}分钟", userId, overtimeMinutes);
                    
                    // 获取用户信息
                    Users user = usersService.getById(userId);
                    if (user != null) {
                        // 记录加班
                        Overtime overtime = overtimeService.recordOvertime(userId, user.getName(), device, now);
                        log.info("用户[{}]下班后打卡，记录加班", user.getName());
                    }
                }
                
                log.info(type == 7 ? "签退成功" : "重复签到");
                return SaResult.data(type == 7 ? "签退成功" : null);
            }
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }

    ///**
    // * 更新脱岗时间
    // */
    //@PostMapping("/leave/post")
    //public SaResult updateLeavePost(@RequestParam String token,
    //                                @RequestParam String equipmentNumber) {
    //    if (!"9999".equals(token)) {
    //        return SaResult.error("token错误");
    //    }
    //    return attendanceService.updateLeavePost(equipmentNumber);
    //
    //}

    /**
     * 获取考勤统计
     *
     * @param userId    员工ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @GetMapping("/stats/{userId}")
    public SaResult getAttendanceStats(
            @PathVariable Integer userId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        return SaResult.data(attendanceService.getAttendanceStats(userId, startDate, endDate));
    }

    ///**
    // * 获取所有员工的考勤记录
    // *
    // * @param startDate 开始日期
    // * @param endDate   结束日期
    // */
    //@GetMapping("/all")
    //public SaResult getAllAttendance(
    //        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
    //        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate,
    //        @RequestParam(required = false) String userName,
    //        @RequestParam(defaultValue = "0") int page,
    //        @RequestParam(defaultValue = "10") int size) {
    //    return SaResult.data(attendanceService.getAllEmployeeAttendance(startDate, endDate));
    //}

    /**
     * 提交请假申请
     *
     * @param userId    员工ID
     * @param leaveType 请假类型（1-病假，2-事假，3-年假，4-调休，5-婚假，6-产假，7-丧假）
     * @param startTime 请假开始时间
     * @param endTime   请假结束时间
     * @param reason    请假原因
     * @return 请假申请结果
     */
    @PostMapping("/leave/apply")
//    @SaCheckPermission("/attendance-management/attendance/leave/apply")
    @Log(title = "提交请假申请", businessType = BusinessType.INSERT)
    public SaResult applyLeave(
            @RequestParam Integer userId,
            @RequestParam String userName,
            @RequestParam Integer leaveType,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String reason) {
        return leaveService.applyLeave(userId, userName, leaveType, startTime, endTime, reason);
    }

    /**
     * 审批请假申请
     *
     * @param leaveId         请假记录ID
     * @param approvalStatus  审批状态（1-同意，2-拒绝）
     * @param approvalComment 审批意见
     * @return 审批结果
     */
    @PostMapping("/leave/approve")
//    @SaCheckPermission("/attendance-management/attendance/leave/approve")
    @Log(title = "审批请假申请", businessType = BusinessType.UPDATE)
    public SaResult approveLeave(
            @RequestParam Integer leaveId,
            @RequestParam Integer approvalStatus,
            @RequestParam(required = false) String approvalComment) {
        return leaveService.approveLeave(leaveId, approvalStatus, approvalComment);
    }

    /**
     * 查询请假记录
     *
     * @param city      城市（可选）
     * @param county    县（可选）
     * @param township  乡镇（可选）
     * @param hamlet    村（可选）
     * @param site      点位（可选）
     * @param userName  用户名（可选）
     * @param status    请假状态（0-待审批，1-已批准，2-已拒绝，3-已取消，可选）
     * @param startDate 开始日期（可选）
     * @param endDate   结束日期（可选）
     * @param page      页码
     * @param size      每页大小
     * @return 请假记录列表
     */
    @GetMapping("/leave/list")
//    @SaCheckPermission("/attendance-management/attendance/leave/list")
    @Log(title = "查询请假记录", businessType = BusinessType.SELECT)
    public SaResult getLeaveRecords(@RequestParam(required = false) String city,
                                    @RequestParam(required = false) String county,
                                    @RequestParam(required = false) String township,
                                    @RequestParam(required = false) String hamlet,
                                    @RequestParam(required = false) String site,
                                    @RequestParam(required = false) String userName,
                                    @RequestParam(required = false) Integer status,
                                    @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
                                    @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
                                    @RequestParam(defaultValue = "1") int page,
                                    @RequestParam(defaultValue = "10") int size) {
        return leaveService.getLeaveRecords(city, county, township, hamlet, site, userName, status, startDate, endDate, page, size);
    }

    /**
     * 获取请假详情
     *
     * @param leaveId 请假记录ID
     * @return 请假详情
     */
    @GetMapping("/leave/{leaveId}")
//    @SaCheckPermission("/attendance-management/attendance/leave/detail")
    @Log(title = "获取请假详情", businessType = BusinessType.SELECT)
    public SaResult getLeaveDetail(@PathVariable Integer leaveId) {
        return leaveService.getLeaveDetail(leaveId);
    }

    /**
     * 取消请假申请
     *
     * @param leaveId 请假记录ID
     * @param reason  取消原因
     * @return 取消结果
     */
    @PostMapping("/leave/cancel")
//    @SaCheckPermission("/attendance-management/attendance/leave/cancel")
    @Log(title = "取消请假申请", businessType = BusinessType.UPDATE)
    public SaResult cancelLeave(
            @RequestParam Integer leaveId,
            @RequestParam(required = false) String reason) {
        return leaveService.cancelLeave(leaveId, reason);
    }

    /**
     * 获取员工的考勤排班信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @SaCheckPermission("/attendance-management/attendance/schedule/all")
    @Log(title = "获取考勤排班信息", businessType = BusinessType.SELECT)
    @GetMapping("/schedule/all")
    public SaResult getEmployeeSchedule(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate,
            @RequestParam String userName) {
        return SaResult.data(attendanceScheduleService.getEmployeeAttendanceSchedule(userName, startDate, endDate));
    }
} 