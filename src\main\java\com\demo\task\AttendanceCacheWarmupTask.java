package com.demo.task;

import com.demo.service.AttendanceCacheService;
import com.demo.service.IllegalRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 考勤缓存预热定时任务
 */
@Slf4j
@Component
public class AttendanceCacheWarmupTask {

    @Autowired
    private AttendanceCacheService attendanceCacheService;

    @Autowired
    private IllegalRecordsService illegalRecordsService;

    /**
     * 预热地区配置：宜宾市（会预热宜宾市下所有地区的数据）
     */
    private static final String WARMUP_CITY = "宜宾市";

    /**
     * 预热天数：固定7天
     */
    private static final int WARMUP_DAYS = 7;

    /**
     * 是否启用缓存预热
     */
    @Value("${attendance.cache.warmup.enabled:true}")
    private boolean warmupEnabled;

    /**
     * 每天凌晨2点执行缓存预热
     * 预热宜宾市最近7天的数据
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void dailyCacheWarmup() {
        if (!warmupEnabled) {
            log.info("缓存预热已禁用，跳过执行");
            return;
        }

        try {
            log.info("开始执行每日缓存预热任务 - 预热地区: {}", WARMUP_CITY);

            LocalDate endDate = LocalDate.now().minusDays(1); // 昨天
            LocalDate startDate = endDate.minusDays(WARMUP_DAYS - 1); // 最近7天

            log.info("预热日期范围: {} 到 {}", startDate, endDate);

            // 直接预热宜宾市的数据，这样会包含宜宾市下所有地区
            warmupCityData(startDate, endDate);

            log.info("每日缓存预热任务完成");

        } catch (Exception e) {
            log.error("每日缓存预热任务执行失败", e);
        }
    }

    /**
     * 每周日凌晨1点执行缓存清理
     */
    @Scheduled(cron = "0 0 1 * * SUN")
    public void weeklyCacheCleanup() {
        if (!warmupEnabled) {
            return;
        }

        try {
            log.info("开始执行每周缓存清理任务");
            attendanceCacheService.cleanExpiredCache();
            log.info("每周缓存清理任务完成");
        } catch (Exception e) {
            log.error("每周缓存清理任务执行失败", e);
        }
    }

    /**
     * 每小时执行一次当天数据的缓存刷新（工作时间）
     * 工作日的8点到18点，每小时刷新一次当天的缓存
     */
    @Scheduled(cron = "0 0 8-18 * * MON-FRI")
    public void hourlyTodayCacheRefresh() {
        if (!warmupEnabled) {
            return;
        }

        try {
            log.info("开始执行当天缓存刷新任务 - 地区: {}", WARMUP_CITY);

            LocalDate today = LocalDate.now();

            // 只刷新今天的数据
            warmupCityData(today, today);

            log.info("当天缓存刷新任务完成");

        } catch (Exception e) {
            log.error("当天缓存刷新任务执行失败", e);
        }
    }

    /**
     * 应用启动后延迟5分钟执行初始缓存预热
     */
    @Scheduled(initialDelay = 5 * 60 * 1000, fixedDelay = Long.MAX_VALUE)
    public void initialCacheWarmup() {
        if (!warmupEnabled) {
            return;
        }

        try {
            log.info("开始执行应用启动后的初始缓存预热 - 地区: {}", WARMUP_CITY);

            LocalDate endDate = LocalDate.now().minusDays(1); // 昨天
            LocalDate startDate = endDate.minusDays(2); // 最近3天

            warmupCityData(startDate, endDate);

            log.info("初始缓存预热任务完成");

        } catch (Exception e) {
            log.error("初始缓存预热任务执行失败", e);
        }
    }

    /**
     * 预热指定城市的数据
     * 通过调用考勤接口来预热缓存
     */
    private void warmupCityData(LocalDate startDate, LocalDate endDate) {
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failCount = 0;

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 跳过未来日期
            if (date.isAfter(LocalDate.now())) {
                continue;
            }

            try {
                // 预热宜宾市的日度数据，这会包含宜宾市下所有地区的数据
                illegalRecordsService.getAttendanceAndWorkTimeByday(WARMUP_CITY, null, null, null, null, date);
                successCount++;

                // 避免过于频繁的请求
                Thread.sleep(10);

            } catch (Exception e) {
                log.warn("预热缓存失败，日期: {}, 地区: {}, 错误: {}", date, WARMUP_CITY, e.getMessage());
                failCount++;
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("缓存预热完成，地区: {}, 耗时: {}ms, 成功: {}, 失败: {}",
                WARMUP_CITY, endTime - startTime, successCount, failCount);
    }
}
