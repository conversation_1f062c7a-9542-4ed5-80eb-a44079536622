<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.RolePermissionMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.RolePermission">
    <!--@mbg.generated-->
    <!--@Table role_permission-->
    <id column="rp_id" jdbcType="INTEGER" property="rpId" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="permission_id" jdbcType="INTEGER" property="permissionId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rp_id, role_id, permission_id
  </sql>

  <select id="selectMap" resultType="java.util.Map">
    SELECT rp.rp_id, rp.role_id, p.permission_id, p.permission_code,p.remarks,p.plate
    FROM role_permission rp
    LEFT JOIN permission p ON p.permission_id = rp.permission_id
    WHERE rp.role_id = #{id}
  </select>
</mapper>