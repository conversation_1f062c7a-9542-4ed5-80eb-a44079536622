package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户班次组实体类，表示用户与班次组的关联关系。
 */
@Data
@TableName("user_shift_group")
public class UserShiftGroup {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID
    
    private Integer userId;    // 员工ID
    private Integer groupId;   // 班次组ID
    private Integer fixedShiftId; // 固定班次ID（如果是轮班制则为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;  // 生效日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;    // 结束日期（null表示长期有效）
    private Integer status;    // 状态：1-生效，0-失效
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime; // 更新时间
} 