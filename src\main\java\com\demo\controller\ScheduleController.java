package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.config.Log;
import com.demo.entity.DTO.ScheduleGenerationRequest;
import com.demo.entity.DTO.IocationDTO;
import com.demo.entity.Schedule;
import com.demo.entity.UserShiftGroup;
import com.demo.enums.BusinessType;
import com.demo.service.ScheduleService;
import com.demo.service.UserShiftGroupService;
import com.demo.service.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 排班控制层
 */
@RestController
@RequestMapping("/schedule")
public class ScheduleController {
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private UsersService usersService;
    @Autowired
    UserShiftGroupService userShiftGroupService;

    /**
     * 获取所有的劝导员
     */
    @SaCheckPermission("/schedule-management/schedule/employees")
    @Log(title = "查询劝导员列表", businessType = BusinessType.SELECT)
    @GetMapping("/employees")
    public SaResult getAllThePersuaders(
            IocationDTO iocationDTO) {
        return usersService.getAllThePersuaders(iocationDTO);
    }

    /**
     * 查看排班详细
     */
    @SaCheckPermission("/schedule-management/schedule/employees/userId")
    @Log(title = "查看排班详细", businessType = BusinessType.SELECT)
    @GetMapping("/employee/{userId}")
    public SaResult getEmployeeSchedule(
            @PathVariable Integer userId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        return SaResult.data(scheduleService.getEmployeeSchedule(userId, startDate, endDate));
    }

    /**
     * 获取某天所有员工的排班情况
     */
    @GetMapping("/date")
    public SaResult getScheduleByDate(@RequestParam LocalDateTime date) {
        return SaResult.data(scheduleService.getScheduleByDate(date));
    }

    /**
     * 修改排班
     */
    @PutMapping("/update")
    public SaResult updateSchedule(@RequestBody Schedule schedule) {
        schedule.setUpdateTime(LocalDateTime.now());
        scheduleService.updateById(schedule);
        return SaResult.data(schedule);
    }

    /**
     * 取消排班
     */
    @SaCheckPermission("/schedule-management/schedule/userId")
    @Log(title = "取消排班", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @Transactional
    public SaResult cancelSchedule(@PathVariable Integer id) {
        // 删除旧排班
        scheduleService.remove(new LambdaQueryWrapper<Schedule>()
                .eq(Schedule::getUserId, id)
                .ge(Schedule::getScheduleDate, DateUtil.today()));
        // 解除与班组的关联
        QueryWrapper<UserShiftGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", id);
        userShiftGroupService.remove(queryWrapper);
        
        return SaResult.ok();
    }

    /**
     * 生成排班（已优化：自动过滤重复排班）
     */
    @PostMapping("/generate")
    public SaResult generateSchedule(@RequestBody ScheduleGenerationRequest request) {
        try {
            // 先检查现有排班数量
            List<Schedule> existingSchedules = scheduleService.list(
                new LambdaQueryWrapper<Schedule>()
                    .eq(Schedule::getUserId, request.getUserId())
                    .ge(Schedule::getScheduleDate, request.getStartDate())
                    .le(Schedule::getScheduleDate, request.getStartDate().plusDays(request.getDays()))
            );

            List<Schedule> schedules = scheduleService.generateSchedule(
                    request.getUserId(),
                    request.getGroupId(),
                    request.getStartDate(),
                    request.getDays()
            );

            // 计算实际新增的排班数量
            int newSchedulesCount = schedules.size();
            int existingCount = existingSchedules.size();

            Map<String, Object> result = new HashMap<>();
            result.put("schedules", schedules);
            result.put("newSchedulesCount", newSchedulesCount);
            result.put("existingCount", existingCount);
            result.put("message", String.format("排班生成完成：新增%d条排班记录，已存在%d条", newSchedulesCount, existingCount));

            return SaResult.data(result);
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }
}
