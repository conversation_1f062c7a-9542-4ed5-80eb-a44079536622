package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.LeavePost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface LeavePostMapper extends BaseMapper<LeavePost> {
    /**
     * 获取指定日期和地点的脱岗统计
     */
    List<Map<String, Object>> getLeavePostStats(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("date") LocalDate date
    );

    /**
     * 获取指定用户在指定日期的脱岗记录
     */
    List<LeavePost> getUserLeavePostsByDate(
            @Param("userId") Integer userId,
            @Param("date") LocalDate date
    );

    /**
     * 获取指定时间范围内的脱岗记录
     */
    List<LeavePost> getLeavePostsByDate(@Param("city") String city,
                                        @Param("county") String county,
                                        @Param("township") String township,
                                        @Param("hamlet") String hamlet,
                                        @Param("site") String site,
                                        @Param("date") LocalDate date);

    /**
     * 获取用户未结束的脱岗记录
     */
    LeavePost getUnfinishedLeavePost(@Param("userId") Integer userId,
                                     @Param("scheduleId") Integer scheduleId);

    /**
     * 更新脱岗记录
     */
    int updateLeavePost(@Param("id") Integer id,
                        @Param("endTime") Date endTime,
                        @Param("duration") Integer duration,
                        @Param("status") Integer status,
                        @Param("updateTime") Date updateTime);

    /**
     * 获取指定日期和地点的请假记录
     */
    List<Map<String, Object>> getLeaveRecordsByDate(
            String city, String county, String township, String hamlet, String site, LocalDate date);
}