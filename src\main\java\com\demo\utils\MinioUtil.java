package com.demo.utils;

import com.demo.config.MinIoClientConfig;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Item;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class MinioUtil {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinIoClientConfig minIoClientConfig;
    @Autowired
    ContentTypeDetector contentTypeDetector;

    /**
     * 判断bucket是否存在，不存在则创建
     */
    public boolean existBucket(String bucketName) {
        boolean exists;
        try {
            exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                exists = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            exists = false;
        }
        return exists;
    }

    /**
     * 删除bucket
     */
    public Boolean removeBucket(String bucketName) {
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 上传文件
     *
     * @param file     文件
     * @param fileName 文件名称
     */
    public String upload(MultipartFile file, String fileName) {
        // 检查文件和文件名是否为空
        if (file == null || file.isEmpty() || fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件或文件名不能为空");
        }
        // 确定文件的 Content-Type
        String contentType = contentTypeDetector.determineContentType(file, fileName);
        // 构建对象键（key），模拟文件夹结构
        //String objectName = folderName + "/" + fileName;
        // 使用putObject上传一个文件到存储桶中。
        try (InputStream inputStream = file.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minIoClientConfig.getBucketName())
                    .object(fileName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(contentType) // 使用确定的 Content-Type
                    .build());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("文件上传失败", e);
        }
        return fileName;
    }

    /**
     * 上传文件
     *
     * @param file     文件
     * @param fileName 文件名称
     */
    public String uploadFile(MultipartFile file, String fileName,String folderName) {
        // 检查文件和文件名是否为空
        if (file == null || file.isEmpty() || fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件或文件名不能为空");
        }
        // 确定文件的 Content-Type
        String contentType = contentTypeDetector.determineContentType(file, fileName);
        // 构建对象键（key），模拟文件夹结构
        String objectName = "";
        if(StringUtils.isNotBlank(folderName)){
             objectName = folderName + "/" + fileName;
        }else{
             objectName = fileName;
        }
        // 使用putObject上传一个文件到存储桶中。
        try (InputStream inputStream = file.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minIoClientConfig.getBucketName())
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(contentType) // 使用确定的 Content-Type
                    .build());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("文件上传失败", e);
        }
        return objectName;
    }

    /**
     * 获取文件访问地址
     *
     * @param fileName 文件名称
     */
    public String getFileUrl(String fileName) {
        try {
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minIoClientConfig.getBucketName())
                    .object(fileName)
                    .build()
            );
            
            //System.out.println("Original URL: " + url); // 添加调试日志
            //
            //// 只转换文件访问URL为HTTPS
            //if (url != null && url.contains("/countryside/")) {
            //    url = url.replace("http://117.175.127.248:9000/countryside/",
            //                    "https://ybda.top/minio/countryside/");
            //    System.out.println("Converted URL: " + url); // 添加调试日志
            //}
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取文件访问地址（有过期时间）
     *
     * @param fileName 文件名称
     * @param time     时间
     * @param timeUnit 时间单位
     */
    public String getExpireFileUrl(String fileName, int time, TimeUnit timeUnit) {
        try {
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minIoClientConfig.getBucketName())
                    .object(fileName)
                    .expiry(time, timeUnit)
                    .build());

//            // 只转换文件访问URL为HTTPS
//            if (url != null && url.contains("/countryside/")) {
//                url = url.replace("http://117.175.127.248:19000/countryside/",
//                                "https://file.yibindaoan.com/minio/countryside/");
//            }
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取文件访问地址（支持HTTPS）
     */
    public String getSecureExpireFileUrl(String fileName, int time, TimeUnit timeUnit) {
        try {
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minIoClientConfig.getBucketName())
                    .object(fileName)
                    .expiry(time, timeUnit)
                    .build());
            
            // 只转换文件访问URL为HTTPS
            if (url != null && url.contains("/countryside/")) {
                url = url.replace("http://117.175.127.248:9000/countryside/", 
                                "https://ybda.top/minio/countryside/");
            }
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 下载文件
     *
     * @param fileName 文件名称
     */
    public void download(HttpServletResponse response, String fileName) {
        InputStream in = null;
        try {
            // 获取对象信息
            StatObjectResponse stat = minioClient.statObject(StatObjectArgs.builder().bucket(minIoClientConfig.getBucketName()).object(fileName).build());
            response.setContentType(stat.contentType());
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 文件下载
            in = minioClient.getObject(GetObjectArgs.builder().bucket(minIoClientConfig.getBucketName()).object(fileName).build());
            IOUtils.copy(in, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名称
     */
    public void delete(String fileName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(minIoClientConfig.getBucketName()).object(fileName).build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取指定文件夹下的所有文件列表（智能模糊匹配，不限制时间）
     *
     * @param folderPath 文件夹路径（自动支持模糊匹配，如"2025-08-0"匹配"2025-08-01", "2025-08-02"等）
     * @return 符合条件的文件列表
     */
    public List<String> listObjectsByTimeRange(String folderPath) {
        return listObjectsByTimeRange(folderPath, null, null);
    }

    /**
     * 获取指定时间段和文件夹下的文件列表（智能模糊匹配）
     *
     * @param folderPath 文件夹路径（自动支持模糊匹配，如"2025-08-0"匹配"2025-08-01", "2025-08-02"等）
     * @param startTime  开始时间（可选，为null时不限制开始时间）
     * @param endTime    结束时间（可选，为null时不限制结束时间）
     * @return 符合条件的文件列表
     */
    public List<String> listObjectsByTimeRange(String folderPath, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> fileList = new ArrayList<>();
        try {
            // 智能判断搜索策略
            String prefix = "";
            boolean useFuzzyMatch = false;

            if (folderPath != null && !folderPath.trim().isEmpty()) {
                // 如果路径以/结尾，说明是完整的文件夹路径，使用精确匹配
                if (folderPath.endsWith("/")) {
                    prefix = folderPath;
                } else {
                    // 否则启用模糊匹配
                    useFuzzyMatch = true;
                    // 找到最后一个完整的路径部分作为前缀
                    int lastSlash = folderPath.lastIndexOf('/');
                    if (lastSlash > 0) {
                        prefix = folderPath.substring(0, lastSlash + 1);
                    } else {
                        prefix = ""; // 从根目录搜索
                    }
                }
            }

            // 列出指定前缀的所有对象
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minIoClientConfig.getBucketName())
                            .prefix(prefix)
                            .recursive(true) // 递归获取所有子文件夹的文件
                            .build()
            );

            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();

                // 跳过文件夹（以/结尾的对象）
                if (objectName.endsWith("/")) {
                    continue;
                }

                // 如果使用模糊匹配，检查路径是否匹配
                if (useFuzzyMatch && folderPath != null && !folderPath.trim().isEmpty()) {
                    if (!isPathMatching(objectName, folderPath)) {
                        continue;
                    }
                }
                    fileList.add(objectName);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取文件列表失败", e);
        }
        return fileList;
    }

    /**
     * 检查路径是否匹配模糊模式
     * 例如：folderPattern="2025-08-0" 可以匹配 "2025-08-01/", "2025-08-02/" 等
     */
    private boolean isPathMatching(String objectPath, String folderPattern) {
        if (folderPattern == null || folderPattern.trim().isEmpty()) {
            return true;
        }
        // 将对象路径按/分割
        String[] pathParts = objectPath.split("/");
        String[] patternParts = folderPattern.split("/");

        // 检查是否有任何路径部分匹配模式
        for (String pathPart : pathParts) {
            for (String patternPart : patternParts) {
                if (pathPart.startsWith(patternPart)) {
                    return true;
                }
            }
        }
        // 也检查整个路径是否包含模式
        return objectPath.contains(folderPattern);
    }

    /**
     * 批量下载文件并打包为ZIP（扁平化结构）
     *
     * @param fileList 文件列表
     * @param response HTTP响应对象
     * @param zipFileName ZIP文件名
     */
    public void downloadMultipleFilesAsZip(List<String> fileList, HttpServletResponse response, String zipFileName) {
        if (fileList == null || fileList.isEmpty()) {
            throw new IllegalArgumentException("文件列表不能为空");
        }

        try {
            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                for (String objectName : fileList) {
                    try {
                        // 获取文件输入流
                        InputStream fileInputStream = minioClient.getObject(
                                GetObjectArgs.builder()
                                        .bucket(minIoClientConfig.getBucketName())
                                        .object(objectName)
                                        .build()
                        );

                        // 生成扁平化的文件名：将路径分隔符替换为下划线
                        String flatFileName = objectName.replace("/", "_");

                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(flatFileName);
                        zipOut.putNextEntry(zipEntry);

                        // 将文件内容写入ZIP
                        IOUtils.copy(fileInputStream, zipOut);

                        // 关闭当前条目和文件流
                        zipOut.closeEntry();
                        fileInputStream.close();

                    } catch (Exception e) {
                        // 记录单个文件处理失败，但继续处理其他文件
                        System.err.println("处理文件失败: " + objectName + ", 错误: " + e.getMessage());
                    }
                }
                zipOut.finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("批量下载文件失败", e);
        }
    }

}