package com.demo.entity.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class ShiftDTO {
    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 班次开始时间
     */
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 班次结束时间
     */
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 班次对应的工作日，使用SET类型表示一周内的多个工作日
     */
    private Set<String> workDays;
}
