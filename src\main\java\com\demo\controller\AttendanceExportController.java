package com.demo.controller;

import com.demo.config.Log;
import com.demo.enums.BusinessType;
import com.demo.service.AttendanceExportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;

/**
 * 考勤数据导出控制器
 */
@RestController
@RequestMapping("/attendance/export")
public class AttendanceExportController {

    @Autowired
    private AttendanceExportService attendanceExportService;

    /**
     * 导出人员考勤明细数据
     * 导出指定时间范围内、指定地区的人员考勤详细数据
     */
//    @SaCheckPermission("/attendance-management/export/personal")
    @Log(title = "导出人员考勤明细数据", businessType = BusinessType.EXPORT)
    @GetMapping("/personal")
    public void exportPersonalAttendance(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletResponse response) throws IOException {
        
        attendanceExportService.exportPersonalAttendance(
                city, county, township, hamlet, site,
                startDate, endDate,
                response);
    }

    /**
     * 导出考勤汇总数据（不包含人员明细）
     * 根据传入的地区层级进行汇总：
     * - 传入市级：汇总区级数据
     * - 传入市+区：汇总镇级数据
     * - 传入市+区+镇：汇总村级数据
     * - 传入市+区+镇+村：汇总人员数据
     */
//    @SaCheckPermission("/attendance-management/export/summary")
    @Log(title = "导出考勤汇总数据", businessType = BusinessType.EXPORT)
    @GetMapping("/summary")
    public void exportSummaryAttendance(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletResponse response) throws IOException {
        
        attendanceExportService.exportSummaryAttendance(
                city, county, township, hamlet, site,
                startDate, endDate,
                response);
    }
    
    /**
     * 导出考勤明细和汇总数据（包含人员明细和汇总）
     * 根据传入的地区层级进行汇总和展示明细：
     * - 传入市级：汇总区级数据，显示各区县明细
     * - 传入市+区：汇总镇级数据，显示各镇明细
     * - 传入市+区+镇：汇总村级数据，显示各村明细
     * - 传入市+区+镇+村：汇总劝导站数据，显示各劝导站明细
     * - 传入市+区+镇+村+站点：显示该站点下所有人员明细
     */
//    @SaCheckPermission("/attendance-management/export/detail-summary")
    @Log(title = "导出考勤明细和汇总数据", businessType = BusinessType.EXPORT)
    @GetMapping("/detail-summary")
    public void exportDetailAndSummary(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletResponse response) throws IOException {
        
        attendanceExportService.exportDetailAndSummary(
                city, county, township, hamlet, site,
                startDate, endDate,
                response);
    }
} 