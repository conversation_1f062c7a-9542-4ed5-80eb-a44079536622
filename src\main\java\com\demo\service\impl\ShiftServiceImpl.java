package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.config.ShiftOperationException;
import com.demo.entity.Shift;
import com.demo.entity.ShiftGroupRelation;
import com.demo.mapper.ShiftGroupRelationMapper;
import com.demo.mapper.ShiftMapper;
import com.demo.service.ShiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.List;

@Service
public class ShiftServiceImpl extends ServiceImpl<ShiftMapper, Shift> implements ShiftService {
    
    @Autowired
    private ShiftMapper shiftMapper;
    
    @Autowired
    private ShiftGroupRelationMapper shiftGroupRelationMapper;

    @Override
    public List<Shift> getShiftsByGroup(Integer groupId) {
        return shiftMapper.selectByGroupId(groupId);
    }
    
    @Override
    public void addShiftToGroup(Integer shiftId, Integer groupId) {
        Shift newShift = this.getById(shiftId);
        if (newShift == null) {
            throw new ShiftOperationException("班次不存在");
        }

        if (!isStartTimeBeforeEndTime(newShift)) {
            throw new ShiftOperationException("开始时间必须在结束时间之前");
        }

        if (hasTimeOverlap(newShift, groupId)) {
            throw new ShiftOperationException("班次时间与现有班次重叠，请检查时间设置");
        }

        ShiftGroupRelation relation = new ShiftGroupRelation();
        relation.setShiftId(shiftId);
        relation.setGroupId(groupId);
        shiftGroupRelationMapper.insert(relation);
    }
    
    @Override
    public void removeShiftFromGroup(Integer shiftId, Integer groupId) {
        shiftGroupRelationMapper.delete(new LambdaQueryWrapper<ShiftGroupRelation>()
            .eq(ShiftGroupRelation::getShiftId, shiftId)
            .eq(ShiftGroupRelation::getGroupId, groupId));
    }

    @Override
    public SaResult selectPage(IPage<Shift> pageRequest) {
        IPage<Shift> shiftIPage = shiftMapper.selectPage(pageRequest, new LambdaQueryWrapper<Shift>());
        return SaResult.data(shiftIPage);
    }

    /**
     * 检查班次时间是否重叠
     */
    private boolean hasTimeOverlap(Shift newShift, Integer groupId) {
        List<Shift> existingShifts = shiftGroupRelationMapper.selectShiftsByGroupId(groupId);
        
        LocalTime newStart = newShift.getStartTime();
        LocalTime newEnd = newShift.getEndTime();
        
        boolean isNewShiftOvernight = newEnd.isBefore(newStart);
        
        for (Shift existing : existingShifts) {
            LocalTime existingStart = existing.getStartTime();
            LocalTime existingEnd = existing.getEndTime();
            boolean isExistingOvernight = existingEnd.isBefore(existingStart);
            
            if (isTimeOverlap(newStart, newEnd, isNewShiftOvernight,
                    existingStart, existingEnd, isExistingOvernight)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查两个时间段是否重叠
     */
    private boolean isTimeOverlap(
            LocalTime start1, LocalTime end1, boolean isOvernight1,
            LocalTime start2, LocalTime end2, boolean isOvernight2) {
        
        if (!isOvernight1 && !isOvernight2) {
            // 都不跨天的情况
            return !(end1.isBefore(start2) || start1.isAfter(end2));
        } else if (isOvernight1 && isOvernight2) {
            // 都跨天的情况
            return true; // 必定有重叠
        } else if (isOvernight1) {
            // 第一个跨天，第二个不跨天
            return !(end1.isBefore(start2) && start1.isAfter(end2));
        } else {
            // 第二个跨天，第一个不跨天
            return !(end2.isBefore(start1) && start2.isAfter(end1));
        }
    }

    // 验证开始时间是否在结束时间之前
    private boolean isStartTimeBeforeEndTime(Shift shift) {
        return shift.getStartTime().isBefore(shift.getEndTime());
    }
}
