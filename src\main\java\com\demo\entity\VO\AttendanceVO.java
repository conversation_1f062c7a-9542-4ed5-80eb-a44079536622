package com.demo.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AttendanceVO {
    private Integer id;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalDateTime checkInTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalDateTime checkOutTime;
    private String status;          // 考勤状态的文字描述
    private String statusCode;      // 状态码，用于前端展示不同颜色
    private String remark;
} 