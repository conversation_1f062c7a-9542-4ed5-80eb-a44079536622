package com.demo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 表示一个通话房间
 * 管理房间内的所有客户端
 */
public class Room {
    private static final Logger log = LoggerFactory.getLogger(Room.class);
    
    private String roomId;
    private Map<String, Client> clients = new ConcurrentHashMap<>();
    
    public Room(String roomId) {
        this.roomId = roomId;
    }

    /**
     * 添加新的客户端到房间
     */
    public void addClient(String clientId) {
        clients.put(clientId, new Client(clientId));
        log.info("房间 {} 添加客户端: {}", roomId, clientId);
    }

    /**
     * 向房间内其他客户端广播ICE候选者信息
     */
    public void broadcastIceCandidate(String fromClientId, String candidate) {
        clients.forEach((clientId, client) -> {
            if(!clientId.equals(fromClientId)) {
                log.debug("房间 {} 转发ICE候选者: {} -> {}", roomId, fromClientId, clientId);
            }
        });
    }

    /**
     * 向房间内其他客户端广播SDP信息
     */
    public void broadcastSDP(String fromClientId, String type, String sdp) {
        clients.forEach((clientId, client) -> {
            if(!clientId.equals(fromClientId)) {
                log.debug("房间 {} 转发SDP: {} -> {}, type={}", roomId, fromClientId, clientId, type);
            }
        });
    }

    /**
     * 移除客户端
     */
    public void removeClient(String clientId) {
        clients.remove(clientId);
        log.info("房间 {} 移除客户端: {}", roomId, clientId);
    }

    /**
     * 通知其他客户端有人离开
     */
    public void notifyPeerLeft(String clientId) {
        clients.forEach((otherClientId, client) -> {
            if (!otherClientId.equals(clientId)) {
                log.info("房间 {} 通知客户端离开: {} -> {}", roomId, clientId, otherClientId);
            }
        });
    }

    public int getClientCount() {
        return clients.size();
    }

    /**
     * 获取房间内所有客户端ID
     */
    public Set<String> getClients() {
        return clients.keySet();
    }
}

/**
 * 表示通话房间中的一个客户端
 */
class Client {
    private String clientId;
    
    public Client(String clientId) {
        this.clientId = clientId;
    }
}
