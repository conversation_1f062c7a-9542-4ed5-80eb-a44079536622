package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.entity.TreeNode;
import com.demo.enums.BusinessType;
import com.demo.service.TreeNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 节点树控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/treeNode")
@Slf4j
public class TreeNodeController {
    @Autowired
    TreeNodeService treeNodeService;

    /**
     * 查询拼装节点树
     */
    @RequestMapping("/selectTreeNode")
    @Log(title = "查询拼装节点树", businessType = BusinessType.SELECT)
    public SaResult selectTreeNode() {
        return treeNodeService.selectTreeNode();
    }

    /**
     * 新增节点
     */
    @RequestMapping("/addTreeNode")
    @Log(title = "新增节点", businessType = BusinessType.INSERT)
    public SaResult addTreeNode(@RequestBody TreeNode node) {
        return treeNodeService.addTreeNode(node);
    }

    /**
     * 修改节点
     */
    @RequestMapping("/updateTreeNode")
    @Log(title = "修改节点", businessType = BusinessType.UPDATE)
    public SaResult updateTreeNode(@RequestBody TreeNode node) {
        return treeNodeService.updateTreeNode(node);
    }

    /**
     * 删除节点
     */
    @Log(title = "删除节点", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteTreeNode")
    public SaResult deleteTreeNode(Integer id) {
        return treeNodeService.deleteTreeNode(id);
    }
}
