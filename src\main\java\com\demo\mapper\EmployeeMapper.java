//package com.demo.mapper;
//
//import com.demo.entity.Employee;
//import org.apache.ibatis.annotations.*;
//
//import java.util.List;
//
//@Mapper
//public interface EmployeeMapper {
//    /**
//     * 获取所有员工
//     * @return 所有员工列表
//     */
//    @Select("SELECT * FROM employee")
//    List<Employee> getAllEmployees();
//
//    /**
//     * 根据ID获取员工
//     * @param id 员工ID
//     * @return 员工对象
//     */
//    @Select("SELECT * FROM employee WHERE id = #{id}")
//    Employee getEmployeeById(Long id);
//
//    /**
//     * 保存员工
//     * @param employee 员工对象
//     * @return 插入的行数
//     */
//    @Insert("INSERT INTO employee (name, position, email) VALUES (#{name}, #{position}, #{email})")
//    @Options(useGeneratedKeys = true, keyProperty = "id")
//    int saveEmployee(Employee employee);
//
//    /**
//     * 更新员工信息
//     * @param employee 更新后的员工对象
//     * @return 更新的行数
//     */
//    @Update("UPDATE employee SET name = #{name}, position = #{position}, email = #{email} WHERE id = #{id}")
//    int updateEmployee(Employee employee);
//
//    /**
//     * 根据ID删除员工
//     * @param id 员工ID
//     * @return 删除的行数
//     */
//    @Delete("DELETE FROM employee WHERE id = #{id}")
//    int deleteEmployee(Long id);
//}
