<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.VillageInformationMapper">

    <resultMap id="BaseResultMap" type="com.demo.entity.VillageInformation">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="county" jdbcType="VARCHAR" property="county" />
        <result column="township" jdbcType="VARCHAR" property="township" />
        <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
        <result column="site" jdbcType="VARCHAR" property="site" />
        <result column="area_of_jurisdiction" jdbcType="VARCHAR" property="areaOfJurisdiction" />
        <result column="registered_population" jdbcType="VARCHAR" property="registeredPopulation" />
        <result column="vehicles" jdbcType="INTEGER" property="vehicles" />
        <result column="drivers" jdbcType="INTEGER" property="drivers" />
        <result column="transport_companies" jdbcType="INTEGER" property="transportCompanies" />
        <result column="non_motor_vehicles" jdbcType="INTEGER" property="nonMotorVehicles" />
        <result column="key_freight_companies" jdbcType="INTEGER" property="keyFreightCompanies" />
        <result column="national_roads" jdbcType="INTEGER" property="nationalRoads" />
        <result column="provincial_roads" jdbcType="INTEGER" property="provincialRoads" />
        <result column="county_roads" jdbcType="INTEGER" property="countyRoads" />
        <result column="township_roads" jdbcType="INTEGER" property="townshipRoads" />
        <result column="village_roads" jdbcType="INTEGER" property="villageRoads" />
        <result column="external_roads" jdbcType="INTEGER" property="externalRoads" />
        <result column="category" jdbcType="VARCHAR" property="category" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, city, county, township, hamlet, site, area_of_jurisdiction, registered_population,
        vehicles, drivers, transport_companies, non_motor_vehicles, key_freight_companies,
        national_roads, provincial_roads, county_roads, township_roads, village_roads,
        external_roads, category, create_time, update_time
    </sql>

</mapper>