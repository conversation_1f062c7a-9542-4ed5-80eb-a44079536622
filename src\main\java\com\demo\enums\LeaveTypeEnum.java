package com.demo.enums;

import lombok.Getter;

/**
 * 请假类型枚举
 */
@Getter
public enum LeaveTypeEnum {
    
    SICK_LEAVE(1, "病假"),
    PERSONAL_LEAVE(2, "事假"),
    ANNUAL_LEAVE(3, "年假"),
    COMPENSATORY_LEAVE(4, "调休"),
    MARRIAGE_LEAVE(5, "婚假"),
    MATERNITY_LEAVE(6, "产假"),
    BEREAVEMENT_LEAVE(7, "丧假");
    
    private final Integer code;
    private final String desc;
    
    LeaveTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(Integer code) {
        for (LeaveTypeEnum type : LeaveTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
} 