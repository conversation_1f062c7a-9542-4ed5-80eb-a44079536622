package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 班次组实体类，表示一组班次的集合。
 */
@Data
@TableName("shift_group")
public class ShiftGroup {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID
    private String groupName;    // 班次组名称，如：三班两倒
    private String groupCode;    // 班次组编码，如：GROUP_3_2
    private String description;  // 描述信息
    private String workPattern;  // 工作模式，如：1,1,0 表示两天工作一天休息
    //private Integer status;      // 状态：1-启用，0-禁用
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime; // 更新时间
} 