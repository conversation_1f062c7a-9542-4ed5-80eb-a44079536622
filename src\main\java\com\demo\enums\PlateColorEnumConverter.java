package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class PlateColorEnumConverter {

    public static class StringToPlateColorEnumConverter implements Converter<String, PlateColorEnum> {
        @Override
        public PlateColorEnum convert(String source) {
            return PlateColorEnum.fromString(source);
        }
    }

    public static class IntegerToPlateColorEnumConverter implements Converter<Integer, PlateColorEnum> {
        @Override
        public PlateColorEnum convert(Integer source) {
            return PlateColorEnum.of(source);
        }
    }
}
