package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.Alarm;

import java.io.IOException;
import java.util.Date;

public interface AlarmService extends IService<Alarm>{


    SaResult selectAlarm(String alarmType, Date startTime, Date endTime, Integer curPage, Integer pageSize);

    SaResult getLatestNews(String city, String county, String township, String hamlet, String site);

    SaResult manualAlarm() throws IOException;

    SaResult confirmAlarm(Integer id);

    SaResult getManualOperation();

    SaResult getIntersection(Integer id);

    SaResult getAlarmPrompt(String city, String county, String township, String hamlet, String site);
}
