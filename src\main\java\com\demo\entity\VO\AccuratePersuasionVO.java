package com.demo.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AccuratePersuasionVO {
    /**
     * UUID
     */
    private String uuid;
    /**
     * 违法名称
     */
    private String illegalName;
    /**
     * 违法记录id
     */
    private String illegalRecordsUuid;

    /**
     * 预处理人员
     */
    private Integer userId;

    /**
     * 预处理人员姓名
     */
    private String userName;

    /**
     * 处理方式(打电话还是上门)
     */
    private String disposalMethod;

    /**
     * 处理状态(0未处理，1已处理)
     */
    private Integer disposalStatus;

    /**
     * 拍照图片地址
     */
    private String imgl;

    /**
     * 实际处理人员
     */
    private Integer actualProcessing;

    /**
     * 实际处理人员姓名
     */
    private String actualProcessingName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 处理时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processingTime;

    /**
     * 截止处理时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineTime;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}
