<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.LeavePostMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.LeavePost">
    <!--@mbg.generated-->
    <!--@Table `leave_post`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="schedule_id" jdbcType="INTEGER" property="scheduleId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="township" jdbcType="VARCHAR" property="township" />
    <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `user_id`, `user_name`, `schedule_id`, `start_time`, `end_time`, `duration`, 
    `status`, `city`, `county`, `township`, `hamlet`, `site`, `create_time`, `update_time`
  </sql>

    <!-- 获取指定日期和地点的脱岗统计 -->
    <select id="getLeavePostStats" resultType="java.util.Map">
        SELECT 
            lp.user_id,
            lp.user_name,
            COUNT(lp.id) as leave_count,
            SUM(lp.duration) as total_duration,
            GROUP_CONCAT(
                CONCAT(
                    DATE_FORMAT(lp.start_time, '%H:%i'), 
                    '-', 
                    CASE WHEN lp.end_time IS NULL THEN '至今' ELSE DATE_FORMAT(lp.end_time, '%H:%i') END,
                    ' (', IFNULL(lp.duration, '进行中'), '分钟)'
                ) 
                ORDER BY lp.start_time SEPARATOR ', '
            ) as leave_periods
        FROM leave_post lp
        WHERE DATE(lp.start_time) = #{date}
        <if test="city != null and city != ''">
            AND lp.city = #{city}
            <if test="county != null and county != ''">
                AND lp.county = #{county}
                <if test="township != null and township != ''">
                    AND lp.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND lp.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND lp.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
        </if>
        GROUP BY lp.user_id, lp.user_name
        ORDER BY total_duration DESC
    </select>
    
    <!-- 获取指定日期和地点的脱岗记录 -->
    <select id="getLeavePostsByDate" resultType="com.demo.entity.LeavePost">
        SELECT lp.*, u.name as user_name
        FROM leave_post lp
        JOIN users u ON lp.user_id = u.user_id
        WHERE u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        AND DATE(lp.start_time) = #{date}
        ORDER BY lp.user_id, lp.start_time
    </select>

    <select id="getUnfinishedLeavePost" resultType="com.demo.entity.LeavePost">
        SELECT * 
        FROM leave_post 
        WHERE user_id = #{userId}
        AND schedule_id = #{scheduleId}
        AND status = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <update id="updateLeavePost">
        UPDATE leave_post 
        SET end_time = #{endTime},
            duration = #{duration},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 获取指定日期和地点的请假记录 -->
    <select id="getLeaveRecordsByDate" resultType="java.util.Map">
        SELECT l.id, l.user_id, l.user_name, l.start_time, l.end_time, 
               l.reason, l.status, l.create_time, l.leave_type
        FROM `leave` l
        JOIN users u ON l.user_id = u.user_id
        WHERE u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        AND DATE(l.start_time) &lt;= #{date}
        AND DATE(l.end_time) &gt;= #{date}
        AND l.status = 1  <!-- 已批准的请假 -->
        ORDER BY l.user_id, l.start_time
    </select>
</mapper>