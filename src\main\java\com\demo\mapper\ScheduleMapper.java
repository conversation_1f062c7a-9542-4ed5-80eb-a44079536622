package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Schedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface ScheduleMapper extends BaseMapper<Schedule> {
    /**
     * 查询指定日期的排班信息
     * @param date 日期
     * @param city 城市
     * @param county 区县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @return 排班列表
     */
    List<Schedule> selectSchedulesByDate(@Param("date") LocalDate date,
                                        @Param("city") String city,
                                        @Param("county") String county,
                                        @Param("township") String township,
                                        @Param("hamlet") String hamlet,
                                        @Param("site") String site);

    List<Schedule> selectSchedulesByDate(@Param("date") LocalDate date);

    /**
     * 查询指定日期和点位的排班信息
     * @param date 日期
     * @param city 城市
     * @param county 区县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @return 排班列表
     */
    List<Schedule> selectSchedulesByDateAndLocation(@Param("date") LocalDate date,
                                                  @Param("city") String city,
                                                  @Param("county") String county,
                                                  @Param("township") String township,
                                                  @Param("hamlet") String hamlet,
                                                  @Param("site") String site);

    List<Schedule> getBasicSchedulingInformation(@Param("userId") Integer userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    List<Schedule> getEmployeeScheduleByUserName(@Param("userName") String userName, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 获取指定地点应到人数
     */
    int getScheduledStaffCount(@Param("city") String city, 
                              @Param("county") String county, 
                              @Param("township") String township, 
                              @Param("hamlet") String hamlet, 
                              @Param("site") String site, 
                              @Param("date") LocalDate date);

    /**
     * 获取指定地点实到人数
     */
    int getPresentStaffCount(@Param("city") String city, 
                            @Param("county") String county, 
                            @Param("township") String township, 
                            @Param("hamlet") String hamlet, 
                            @Param("site") String site, 
                            @Param("date") LocalDate date);

    /**
     * 获取指定地点在岗人数
     */
    int getOnDutyStaffCount(@Param("city") String city, 
                           @Param("county") String county, 
                           @Param("township") String township, 
                           @Param("hamlet") String hamlet, 
                           @Param("site") String site, 
                           @Param("date") LocalDate date);

    /**
     * 获取指定日期的排班记录
     */
    List<Schedule> getScheduleByDate(@Param("date") LocalDate date, 
                                   @Param("offset") int offset, 
                                   @Param("limit") int limit);

    /**
     * 获取指定日期的排班记录总数
     */
    int getScheduleCountByDate(@Param("date") LocalDate date);

    /**
     * 根据用户ID和时间范围查询排班记录
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 排班记录列表
     */
    List<Schedule> getSchedulesByUserIdAndTimeRange(@Param("userId") Integer userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<Map<String, Object>> queryWorkingHours(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                @Param("hamlet") String hamlet, @Param("site") String site, @Param("startDate") LocalDate startDate,
                                                @Param("endDate") LocalDate endDate, @Param("theNextLevel") String theNextLevel);

    /**
     * 根据地区和日期范围获取排班信息
     *
     * @param city 市
     * @param county 县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班信息列表
     */
    List<Schedule> getSchedulesByAreaAndDate(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * 检查排班是否已存在
     * @param userId 用户ID
     * @param scheduleDate 排班日期
     * @param shiftId 班次ID
     * @return 存在的记录数量
     */
    int countExistingSchedule(@Param("userId") Integer userId,
                             @Param("scheduleDate") LocalDateTime scheduleDate,
                             @Param("shiftId") Integer shiftId);

    /**
     * 批量检查排班是否存在
     * @param schedules 排班列表
     * @return 已存在的排班记录
     */
    List<Schedule> findExistingSchedules(@Param("schedules") List<Schedule> schedules);
}