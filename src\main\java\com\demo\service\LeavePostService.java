package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.LeavePost;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 脱岗记录服务接口
 */
@Service
public interface LeavePostService extends IService<LeavePost> {
    
    /**
     * 开始脱岗记录
     * @param userId 用户ID
     * @param scheduleId 排班ID
     * @param startTime 脱岗开始时间
     * @return 脱岗记录ID
     */
    Integer startLeavePost(Integer userId, Integer scheduleId, LocalDateTime startTime);
    
    /**
     * 结束脱岗记录
     * @param userId 用户ID
     * @param scheduleId 排班ID
     * @param endTime 脱岗结束时间
     * @return 是否成功
     */
    boolean endLeavePost(Integer userId, Integer scheduleId, LocalDateTime endTime);
    
    /**
     * 根据排班ID结束脱岗记录
     * @param scheduleId 排班ID
     * @param endTime 脱岗结束时间
     * @return 是否成功
     */
    boolean endLeavePostByScheduleId(Integer scheduleId, LocalDateTime endTime);
    
    /**
     * 获取脱岗统计
     */
    SaResult getLeavePostStats(String city, String county, String township, 
                              String hamlet, String site, LocalDate date);
    
    /**
     * 获取用户脱岗记录
     */
    SaResult getUserLeavePostsByDate(Integer userId, LocalDate date);

}