package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户角色表
 */
@Data
@TableName(value = "role_users")
public class RoleUsers {
    /**
     * id
     */
    @TableId(value = "ru_id", type = IdType.AUTO)
    private Integer ruId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    private Integer roleId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Integer userId;
}