package com.demo.service.impl;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import com.demo.service.Room;
import com.demo.service.WebRTCService;
import com.demo.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WebRTCServiceImpl implements WebRTCService {

    // 房间映射表
    private final Map<String, Room> rooms = new ConcurrentHashMap<>();
    @Autowired
    RedisUtils redisUtils;
    @Override
    public SaResult createRoom(String roomId) {
        if (rooms.containsKey(roomId)) {
            log.info("房间已存在: {}", roomId);
            return SaResult.ok("房间已存在");  // 改为返回成功，允许重复创建
        }
        rooms.put(roomId, new Room(roomId));
        log.info("成功创建房间: {}", roomId);
        return SaResult.ok("创建房间成功");
    }

    @Override
    public SaResult joinRoom(String roomId, String clientId) {
        // 如果房间不存在，自动创建
        if (!rooms.containsKey(roomId)) {
            log.info("房间不存在，自动创建: {}", roomId);
            rooms.put(roomId, new Room(roomId));
        }

        Room room = rooms.get(roomId);
        room.addClient(clientId);

        log.info("客户端加入房间: roomId={}, clientId={}, 当前房间人数={}",
                roomId, clientId, room.getClients().size());

        // 通知房间内其他客户端有新成员加入
        for (String otherClientId : room.getClients()) {
            if (!otherClientId.equals(clientId)) {
                sendMessage(roomId, otherClientId, createMessage("peer-joined", roomId, clientId, null));
            }
        }

        return SaResult.ok("加入房间成功");
    }

    @Override
    public SaResult leaveRoom(String roomId, String clientId) {
        if (!rooms.containsKey(roomId)) {
            return SaResult.error("房间不存在");
        }

        Room room = rooms.get(roomId);
        room.removeClient(clientId);

        log.info("客户端离开房间: roomId={}, clientId={}, 当前房间人数={}",
                roomId, clientId, room.getClients().size());

        // 通知房间内其他客户端有成员离开
        for (String otherClientId : room.getClients()) {
            sendMessage(roomId, otherClientId, createMessage("peer-left", roomId, clientId, null));
        }

        // 如果房间为空，删除房间
        if (room.getClients().isEmpty()) {
            rooms.remove(roomId);
            log.info("房间已空，删除房间: {}", roomId);
        }

        return SaResult.ok("离开房间成功");
    }

    @Override
    public SaResult sendOffer(String roomId, String fromClientId, String sdp) {
        if (!rooms.containsKey(roomId)) {
            return SaResult.error("房间不存在");
        }

        Room room = rooms.get(roomId);

        // 向房间内其他客户端发送offer
        for (String toClientId : room.getClients()) {
            if (!toClientId.equals(fromClientId)) {
                Map<String, Object> message = createMessage("offer", roomId, fromClientId, null);
                message.put("sdp", sdp);
                sendMessage(roomId, toClientId, message);

                log.debug("发送offer: from={}, to={}, roomId={}", fromClientId, toClientId, roomId);
            }
        }

        return SaResult.ok("发送offer成功");
    }

    @Override
    public SaResult sendAnswer(String roomId, String fromClientId, String sdp) {
        if (!rooms.containsKey(roomId)) {
            return SaResult.error("房间不存在");
        }

        Room room = rooms.get(roomId);

        // 向房间内其他客户端发送answer
        for (String toClientId : room.getClients()) {
            if (!toClientId.equals(fromClientId)) {
                Map<String, Object> message = createMessage("answer", roomId, fromClientId, null);
                message.put("sdp", sdp);
                sendMessage(roomId, toClientId, message);

                log.debug("发送answer: from={}, to={}, roomId={}", fromClientId, toClientId, roomId);
            }
        }

        return SaResult.ok("发送answer成功");
    }

    @Override
    public SaResult sendIceCandidate(String roomId, String fromClientId, Map<String, Object> candidate) {
        if (!rooms.containsKey(roomId)) {
            return SaResult.error("房间不存在");
        }

        Room room = rooms.get(roomId);

        // 向房间内其他客户端发送ICE候选者
        for (String toClientId : room.getClients()) {
            if (!toClientId.equals(fromClientId)) {
                Map<String, Object> message = createMessage("ice-candidate", roomId, fromClientId, null);
                message.put("candidate", candidate);
                sendMessage(roomId, toClientId, message);

                log.debug("发送ICE候选者: from={}, to={}, roomId={}", fromClientId, toClientId, roomId);
            }
        }

        return SaResult.ok("发送ICE候选者成功");
    }

    @Override
    public Set<String> getRoomClients(String roomId) {
        if (!rooms.containsKey(roomId)) {
            return Collections.emptySet();
        }
        return rooms.get(roomId).getClients();
    }

    // 创建消息
    private Map<String, Object> createMessage(String type, String roomId, String fromClientId, String toClientId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", type);
        message.put("roomId", roomId);
        message.put("clientId", fromClientId);
        if (toClientId != null) {
            message.put("toClientId", toClientId);
        }
        message.put("timestamp", System.currentTimeMillis());
        return message;
    }

    // 发送消息到客户端的消息队列
    private void sendMessage(String roomId, String clientId, Map<String, Object> message) {
        // 实现消息发送逻辑
    }

    /**
     * 请求语音通话
     */
    @Override
    public SaResult requestWebRTC(String roomId) {
        if (redisUtils.get(roomId) != null) {
            return SaResult.ok("请求成功");
        }
        return SaResult.error("请求失败");
    }
} 