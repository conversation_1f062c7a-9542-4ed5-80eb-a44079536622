package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.LeavePost;
import com.demo.entity.Schedule;
import com.demo.entity.Users;
import com.demo.mapper.LeavePostMapper;
import com.demo.service.LeavePostService;
import com.demo.service.ScheduleService;
import com.demo.service.UsersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LeavePostServiceImpl extends ServiceImpl<LeavePostMapper, LeavePost> implements LeavePostService {

    @Autowired
    private LeavePostMapper leavePostMapper;
    
    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private UsersService usersService;

    @Override
    @Transactional
    public Integer startLeavePost(Integer userId, Integer scheduleId, LocalDateTime startTime) {
        // 检查是否已有未结束的脱岗记录
        LeavePost existingRecord = this.getOne(new LambdaQueryWrapper<LeavePost>()
                .eq(LeavePost::getUserId, userId)
                .eq(LeavePost::getScheduleId, scheduleId)
                .eq(LeavePost::getStatus, 0) // 未结束状态
                .orderByDesc(LeavePost::getCreateTime)
                .last("LIMIT 1"));
                
        if (existingRecord != null) {
//            log.info("用户 {} 已有未结束的脱岗记录，忽略本次记录", userId);
            return existingRecord.getId();
        }
        
        // 获取用户信息
        Users user = usersService.getById(userId);
        if (user == null) {
            log.error("未找到用户信息: {}", userId);
            return null;
        }
        
        // 获取排班信息
        Schedule schedule = scheduleService.getById(scheduleId);
        if (schedule == null) {
            log.error("未找到排班信息: {}", scheduleId);
            return null;
        }
        
        // 创建脱岗记录
        LeavePost leavePost = new LeavePost();
        leavePost.setUserId(userId);
        leavePost.setUserName(user.getName());
        leavePost.setScheduleId(scheduleId);
        leavePost.setStartTime(java.sql.Timestamp.valueOf(startTime));  // 转换为Date类型
        leavePost.setStatus(false); // 未结束，使用Boolean类型
        leavePost.setCity(user.getCity());
        leavePost.setCounty(user.getCounty());
        leavePost.setTownship(user.getTownship());
        leavePost.setHamlet(user.getHamlet());
        leavePost.setSite(user.getSite());
        leavePost.setCreateTime(new Date()); // 使用当前时间
        
        this.save(leavePost);
        log.info("用户 {} 开始脱岗记录，ID: {}", userId, leavePost.getId());
        
        return leavePost.getId();
    }

    @Override
    @Transactional
    public boolean endLeavePost(Integer userId, Integer scheduleId, LocalDateTime endTime) {
        // 查找未结束的脱岗记录
        LeavePost leavePost = leavePostMapper.getUnfinishedLeavePost(userId, scheduleId);
                
        if (leavePost == null) {
            log.info("用户 {} 没有未结束的脱岗记录", userId);
            return false;
        }
        
        // 计算脱岗时长（分钟）
        long minutes = Duration.between(
            leavePost.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            endTime
        ).toMinutes();
        
        // 更新脱岗记录
        Date now = new Date();
        int rows = leavePostMapper.updateLeavePost(
            leavePost.getId(),
            java.sql.Timestamp.valueOf(endTime),
            (int) minutes,
            1,  // 已结束状态
            now
        );
        
        boolean result = rows > 0;
        if (result) {
            log.info("用户 {} 结束脱岗记录，ID: {}, 时长: {} 分钟", userId, leavePost.getId(), minutes);
        } else {
            log.warn("用户 {} 更新脱岗记录失败，ID: {}", userId, leavePost.getId());
        }
        
        return result;
    }

    @Override
    public SaResult getLeavePostStats(String city, String county, String township, String hamlet, String site, LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        
        List<Map<String, Object>> stats = leavePostMapper.getLeavePostStats(city, county, township, hamlet, site, date);
        return SaResult.data(stats);
    }

    @Override
    public SaResult getUserLeavePostsByDate(Integer userId, LocalDate date) {
        List<LeavePost> records = leavePostMapper.getUserLeavePostsByDate(userId, date);
        return SaResult.data(records);
    }



    @Transactional
    public boolean saveBatch(List<LeavePost> leavePosts) {
        if (leavePosts == null || leavePosts.isEmpty()) {
            return true;
        }
        
        return super.saveBatch(leavePosts);
    }

    /**
     * 根据排班ID结束脱岗记录
     */
    @Override
    @Transactional
    public boolean endLeavePostByScheduleId(Integer scheduleId, LocalDateTime endTime) {
        log.info("开始结束排班 {} 的脱岗记录，结束时间: {}", scheduleId, endTime);
        
        // 查找该排班下所有未结束的脱岗记录
        List<LeavePost> leavePosts = this.list(new LambdaQueryWrapper<LeavePost>()
                .eq(LeavePost::getScheduleId, scheduleId)
                .eq(LeavePost::getStatus, 0)); // 未结束状态
                
        if (leavePosts.isEmpty()) {
            log.info("排班 {} 没有未结束的脱岗记录", scheduleId);
            return true;
        }
        
        log.info("找到排班 {} 的未结束脱岗记录: {} 条", scheduleId, leavePosts.size());
        
        boolean allSuccess = true;
        for (LeavePost leavePost : leavePosts) {
            // 计算脱岗时长（分钟）
            long minutes = Duration.between(
                leavePost.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                endTime
            ).toMinutes();
            
            log.info("脱岗记录 {} 的时长: {} 分钟", leavePost.getId(), minutes);
            
            // 更新脱岗记录
            Date now = new Date();
            int rows = leavePostMapper.updateLeavePost(
                leavePost.getId(),
                java.sql.Timestamp.valueOf(endTime),
                (int) minutes,
                1,  // 已结束状态
                now
            );
            
            if (rows <= 0) {
                log.warn("结束排班 {} 的脱岗记录失败，ID: {}", scheduleId, leavePost.getId());
                allSuccess = false;
            } else {
                log.info("结束排班 {} 的脱岗记录成功，ID: {}, 时长: {} 分钟", scheduleId, leavePost.getId(), minutes);
            }
        }
        
        log.info("结束排班 {} 的脱岗记录完成，结果: {}", scheduleId, allSuccess ? "全部成功" : "部分失败");
        return allSuccess;
    }
} 