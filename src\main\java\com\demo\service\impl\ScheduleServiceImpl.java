package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.*;
import com.demo.entity.VO.ScheduleVO;
import com.demo.mapper.ScheduleMapper;
import com.demo.mapper.ShiftGroupRelationMapper;
import com.demo.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper, Schedule> implements ScheduleService {
    
    @Autowired
    private ShiftService shiftService;        // 班次服务
    
    @Autowired
    private UsersService userService;         // 用户服务
    
    @Autowired
    private ShiftGroupService shiftGroupService;  // 班次组服务
    
    @Autowired
    private UserShiftGroupService userShiftGroupService;  // 用户班次组关系服务
    
    @Autowired
    private ShiftGroupRelationMapper shiftGroupRelationMapper; // 注入 ShiftGroupRelationMapper

    @Autowired
    private ScheduleMapper scheduleMapper;
    /**
     * 获取员工的排班信息
     * @param userId 员工ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班信息列表（包含详细信息）
     */
    @Override
    public List<ScheduleVO> getEmployeeSchedule(Integer userId, LocalDateTime startDate, LocalDateTime endDate) {
        // 获取基础排班信息
        List<Schedule> schedules=scheduleMapper.getBasicSchedulingInformation(userId, startDate, endDate);
        // 转换为VO对象
        return schedules.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScheduleVO> getEmployeeScheduleByUserName(String userName, LocalDateTime startDate, LocalDateTime endDate) {
        List<Schedule> schedules=scheduleMapper.getEmployeeScheduleByUserName(userName, startDate, endDate);
        // 转换为VO对象
        return schedules.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    
    /**
     * 将排班记录转换为视图对象
     * 包含用户信息、班次信息等
     */
    @Override
    public ScheduleVO convertToVO(Schedule schedule) {
        ScheduleVO vo = new ScheduleVO();
        BeanUtils.copyProperties(schedule, vo);
        
        // 获取用户信息
        Users user = userService.getById(schedule.getUserId());
        if (user == null) {
            throw new RuntimeException("未找到用户信息");
        }
        vo.setUserName(user.getName());
        vo.setDeptName(user.getDeptName());
        
        // 获取班次信息
        Shift shift = shiftService.getById(schedule.getShiftId());
        if (shift == null) {
            throw new RuntimeException("未找到班次信息");
        }
        vo.setShiftName(shift.getShiftName());
        vo.setStartTime(shift.getStartTime());
        vo.setEndTime(shift.getEndTime());
        
        // 获取用户所属的班组信息
        List<UserShiftGroup> userGroups = userShiftGroupService.getUserGroups(schedule.getUserId(), schedule.getScheduleDate());
        if (userGroups.isEmpty()) {
            // 如果找不到班组信息，设置为未知状态而不是抛出异常
            vo.setWorkStatus("已排班");  // 因为既然有排班记录，说明当时是工作日
            return vo;
        }
        
        // 使用找到的班组信息来确定工作状态
        ShiftGroup group = shiftGroupService.getById(userGroups.get(0).getGroupId());
        if (group == null) {
            vo.setWorkStatus("已排班");  // 同样，有排班记录就表示是工作日
            return vo;
        }
        
        String[] workPattern = group.getWorkPattern().split(",");
        int dayIndex = getDayIndex(schedule.getScheduleDate());
        vo.setWorkStatus("1".equals(workPattern[dayIndex % workPattern.length]) ? "工作日" : "休息日");
        
        return vo;
    }

    /**
     * 获取某天所有员工的排班情况
     */
    @Override
    public List<Schedule> getScheduleByDate(LocalDateTime date) {
        LocalDateTime startOfDay = date.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return this.list(new LambdaQueryWrapper<Schedule>()
                .between(Schedule::getScheduleDate, startOfDay, endOfDay)
                .orderByAsc(Schedule::getUserId));
    }
    
    /**
     * 获取日期在工作模式中的索引
     */
    private int getDayIndex(LocalDateTime date) {
        return date.getDayOfYear();
    }

    /**
     * 获取员工当天的排班记录
     */
    @Override
    public Schedule getTodaySchedule(Integer userId, LocalDateTime now) {
        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return this.getOne(new LambdaQueryWrapper<Schedule>()
                .eq(Schedule::getUserId, userId)
                .between(Schedule::getScheduleDate, startOfDay, endOfDay)
                .orderByAsc(Schedule::getScheduleDate)
                .last("LIMIT 1"));
    }

    /**
     * 基于用户当前班次组生成排班
     */
    @Override
    @Transactional
    public List<Schedule> generateSchedule(Integer userId, LocalDateTime startDate, Integer days) {
        // 1. 获取员工当前的班次组
        UserShiftGroup userGroup = userShiftGroupService.getCurrentGroup(userId, startDate);
        if (userGroup == null) {
            throw new RuntimeException("员工未分配班次组");
        }
        
        // 2. 基于班次组生成排班
        return generateSchedule(userId, userGroup.getGroupId(), startDate, days);
    }
    
    /**
     * 指定班次组生成排班
     * 支持固定班次和轮班制
     */
    @Override
    @Transactional
    public List<Schedule> generateSchedule(Integer userId, Integer groupId, LocalDateTime startDate, Integer days) {
        // 1. 获取班次组信息
        ShiftGroup group = shiftGroupService.getById(groupId);
        if (group == null) {
            throw new RuntimeException("班次组不存在");
        }
        
        // 2. 获取用户班组关系
        UserShiftGroup userGroup = userShiftGroupService.getCurrentGroup(userId, startDate);
        if (userGroup == null) {
            throw new RuntimeException("员工未分配班次组");
        }
        
        // 3. 获取班次信息
        List<Shift> shifts;
        if (userGroup.getFixedShiftId() != null) {
            // 固定班次，但获取班组下的所有班次
            shifts = shiftGroupRelationMapper.selectShiftsByGroupId(groupId);
        } else {
            // 轮班制，通过中间表获取班次
            shifts = shiftGroupRelationMapper.selectShiftsByGroupId(groupId);
        }
        
        if (shifts.isEmpty()) {
            throw new RuntimeException("该班次组下没有班次");
        }
        
        // 4. 解析工作模式
        String[] workPattern = group.getWorkPattern().split(",");
        int patternLength = workPattern.length;
        
        // 5. 生成排班
        List<Schedule> schedules = new ArrayList<>();
        LocalDateTime currentDate = startDate;
        Users byId = userService.getById(userId);

        for (int i = 0; i < days; i++) {
            // 判断当天是否为工作日
            int dayIndex = i % patternLength;
            if ("1".equals(workPattern[dayIndex])) {  // 工作日
                // 为每个班次创建排班记录
                for (Shift shift : shifts) {
                    Schedule schedule = new Schedule();
                    schedule.setUserId(userId);
                    schedule.setUserName(byId.getName());
                    schedule.setShiftId(shift.getId());
                    schedule.setScheduleDate(currentDate);
                    schedule.setCreateTime(LocalDateTime.now());
                    schedules.add(schedule);
                }
            }
            // 移到下一天
            currentDate = currentDate.plusDays(1);
        }
        
        // 6. 过滤已存在的排班记录，避免重复
        List<Schedule> filteredSchedules = filterExistingSchedules(schedules);

        if (filteredSchedules.isEmpty()) {
            log.info("用户[{}]在指定时间段内的排班已全部存在，无需重复生成", userId);
            return schedules; // 返回原始列表，但实际未保存重复数据
        }

        // 7. 批量保存过滤后的排班记录
        try {
            this.saveBatch(filteredSchedules);
            log.info("用户[{}]成功生成{}条排班记录，过滤重复{}条", userId, filteredSchedules.size(), schedules.size() - filteredSchedules.size());
        } catch (Exception e) {
            // 如果批量保存失败（可能是数据库约束冲突），尝试逐条保存
            log.warn("批量保存排班失败，尝试逐条保存: {}", e.getMessage());
            List<Schedule> savedSchedules = new ArrayList<>();
            for (Schedule schedule : filteredSchedules) {
                try {
                    this.save(schedule);
                    savedSchedules.add(schedule);
                } catch (Exception ex) {
                    log.warn("保存排班记录失败，用户[{}]，日期[{}]，班次[{}]: {}",
                            schedule.getUserId(), schedule.getScheduleDate(), schedule.getShiftId(), ex.getMessage());
                }
            }
            return savedSchedules;
        }

        return filteredSchedules;
    }


    @Override
    public boolean hasScheduleForShift(Integer shiftId) {
        return this.count(new LambdaQueryWrapper<Schedule>()
                .eq(Schedule::getShiftId, shiftId)) > 0;
    }

    @Override
    public List<Integer> getUserIdsByShiftId(Integer shiftId) {
        return this.list(new LambdaQueryWrapper<Schedule>()
                        .eq(Schedule::getShiftId, shiftId))
                .stream()
                .map(Schedule::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检查排班是否已存在
     */
    @Override
    public boolean isScheduleExists(Integer userId, LocalDateTime scheduleDate, Integer shiftId) {
        return scheduleMapper.countExistingSchedule(userId, scheduleDate, shiftId) > 0;
    }

    /**
     * 过滤已存在的排班记录
     */
    @Override
    public List<Schedule> filterExistingSchedules(List<Schedule> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询已存在的排班记录
        List<Schedule> existingSchedules = scheduleMapper.findExistingSchedules(schedules);

        // 创建已存在排班的标识集合
        Set<String> existingKeys = existingSchedules.stream()
                .map(s -> s.getUserId() + "_" + s.getScheduleDate().toLocalDate() + "_" + s.getShiftId())
                .collect(Collectors.toSet());

        // 过滤掉已存在的排班记录
        return schedules.stream()
                .filter(s -> !existingKeys.contains(s.getUserId() + "_" + s.getScheduleDate().toLocalDate() + "_" + s.getShiftId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Schedule> getTodaySchedules(Integer userId, LocalDate date) {
        QueryWrapper<Schedule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.between("schedule_date", date.atStartOfDay(), date.plusDays(1).atStartOfDay().minusNanos(1));
        return scheduleMapper.selectList(queryWrapper);
    }

    @Override
    public int getScheduledStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date) {
        // 查询当天排班的总人数
        return scheduleMapper.getScheduledStaffCount(city, county, township, hamlet, site, date);
    }

    @Override
    public int getPresentStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date) {
        // 查询当天已签到的人数
        return scheduleMapper.getPresentStaffCount(city, county, township, hamlet, site, date);
    }

    @Override
    public int getOnDutyStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date) {
        // 查询当前在岗的人数
        return scheduleMapper.getOnDutyStaffCount(city, county, township, hamlet, site, date);
    }

    @Override
    public List<Schedule> getScheduleByDate(LocalDate date, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 使用Mapper查询数据
        return scheduleMapper.getScheduleByDate(date, offset, pageSize);
    }

    @Override
    public Schedule getCurrentSchedule(Integer userId) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate();
        
        // 先获取用户今天的所有排班
        List<Schedule> todaySchedules = this.list(new LambdaQueryWrapper<Schedule>()
                .eq(Schedule::getUserId, userId)
                .between(Schedule::getScheduleDate, 
                        today.atStartOfDay(),
                        today.plusDays(1).atStartOfDay().minusNanos(1)));
        
        if (todaySchedules.isEmpty()) {
            return null;
        }
        
        // 遍历排班记录，找到当前时间对应的排班
        for (Schedule schedule : todaySchedules) {
            // 获取班次信息
            Shift shift = shiftService.getById(schedule.getShiftId());
            if (shift == null) {
                continue;
            }
            
            // 计算实际的开始和结束时间
            LocalDateTime scheduleStartTime = today.atTime(shift.getStartTime());
            LocalDateTime scheduleEndTime = today.atTime(shift.getEndTime());
            
            // 处理跨天的情况
            if (shift.getEndTime().isBefore(shift.getStartTime())) {
                scheduleEndTime = scheduleEndTime.plusDays(1);
            }
            
            // 判断当前时间是否在排班时间范围内
            if (now.isAfter(scheduleStartTime) && now.isBefore(scheduleEndTime)) {
                return schedule;
            }
        }
        
        return null;
    }

}
