package com.demo.entity.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;

/**
 * 考勤明细和汇总导出Excel的数据模型
 */
@Data
@HeadStyle(fillForegroundColor = 9)  // 淡蓝色表头
@HeadFontStyle(fontHeightInPoints = 12, bold = BooleanEnum.TRUE)
public class AttendanceExportDetailDTO {
    
    /**
     * 区县 - 导出为Excel列
     */
    @ColumnWidth(15)
    private String countyDisplay;

    /**
     * 镇 - 导出为Excel列
     */
    @ColumnWidth(15)
    private String townshipDisplay;
    
    /**
     * 村 - 导出为Excel列
     */
    @ColumnWidth(15)
    private String hamletDisplay;
    
    /**
     * 劝导站 - 导出为Excel列
     */
    @ColumnWidth(15)
    private String siteDisplay;

    /**
     * 地区 - 不再导出到Excel
     */
    @ExcelIgnore
    private String area;

    /**
     * 员工姓名
     */
    @ColumnWidth(15)
    private String userName;
    
    /**
     * 应上岗总时间
     */
    @ColumnWidth(20)
    private Double scheduledTotalHours;

    /**
     * 实际上岗总总时间
     */
    @ColumnWidth(20)
    private Double actualTotalHours;
    
    /**
     * 在岗率(%)
     */
    @ColumnWidth(15)
    private Double attendanceRate;

    /**
     * 加班总时间
     */
    @ColumnWidth(20)
    private Double overtimeTotalHours;
    
    /**
     * 合计在岗时间 = 实际上岗总时长 + 加班总时长
     */
    @ColumnWidth(20)
    private Double totalOnDutyHours;
    
    /**
     * 合计在岗率(%)
     */
    @ColumnWidth(15)
    private Double totalAttendanceRate;
    
    /**
     * 是否为汇总行 - 不导出到Excel
     */
    @ExcelIgnore
    private Boolean isSummary = false;

    /**
     * 汇总类型：1-镇级汇总，2-村级汇总，0-个人数据 - 不导出到Excel
     */
    @ExcelIgnore
    private Integer summaryType;

    /**
     * 脱岗总时长(小时) - 不导出到Excel
     */
    @ExcelIgnore
    private Double leavePostTotalHours;

    /**
     * 县 - 不导出到Excel（仅用于内部处理）
     */
    @ExcelIgnore
    private String county;

    /**
     * 镇 - 不导出到Excel（仅用于内部处理）
     */
    @ExcelIgnore
    private String township;

    /**
     * 村 - 不导出到Excel（仅用于内部处理）
     */
    @ExcelIgnore
    private String hamlet;
    
    /**
     * 站点 - 不导出到Excel（仅用于内部处理）
     */
    @ExcelIgnore
    private String site;

    /**
     * 上班时间内设备掉线总时长（小时）
     */
    @ColumnWidth(18)
    private Double deviceOfflineHours;
} 