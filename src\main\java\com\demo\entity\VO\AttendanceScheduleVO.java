package com.demo.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class AttendanceScheduleVO {
    private Long scheduleId;
    private Integer userId;
    private String userName;
    private String deptName;
    private Long shiftId;
    private String shiftName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime scheduleDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime endTime;
    private String workStatus;
    private AttendanceVO attendance;
} 