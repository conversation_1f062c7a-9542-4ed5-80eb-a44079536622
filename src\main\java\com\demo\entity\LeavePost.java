package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 脱岗记录表
 */
@Data
@TableName(value = "`leave_post`")
public class LeavePost {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField(value = "`user_id`")
    private Integer userId;

    /**
     * 用户姓名
     */
    @TableField(value = "`user_name`")
    private String userName;

    /**
     * 排班ID
     */
    @TableField(value = "`schedule_id`")
    private Integer scheduleId;

    /**
     * 脱岗开始时间
     */
    @TableField(value = "`start_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 脱岗结束时间
     */
    @TableField(value = "`end_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 脱岗时长（分钟）
     */
    @TableField(value = "`duration`")
    private Integer duration;

    /**
     * 状态：0-未结束，1-已结束
     */
    @TableField(value = "`status`")
    private Boolean status;

    /**
     * 城市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 区县
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡镇
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村庄
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 站点
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}