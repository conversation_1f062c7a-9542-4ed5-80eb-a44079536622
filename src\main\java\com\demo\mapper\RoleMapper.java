package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoleMapper extends BaseMapper<Role> {
    List<Role> selectRolesWithPermissions(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize, @Param("roleCode") String roleCode, @Param("roleName") String roleName);

    long countRoles(@Param("roleCode") String roleCode, @Param("roleName") String roleName);
}