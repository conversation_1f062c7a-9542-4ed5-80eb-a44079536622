<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.PermissionMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Permission">
    <!--@mbg.generated-->
    <!--@Table `permission`-->
    <id column="permission_id" jdbcType="INTEGER" property="permissionId" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="plate" jdbcType="VARCHAR" property="plate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `permission_id`, `permission_code`, `remarks`, `plate`
  </sql>
</mapper>