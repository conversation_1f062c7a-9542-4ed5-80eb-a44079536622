package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 班次实体类，表示具体的工作班次。
 */
@Data
@TableName("shift")
public class Shift {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID
    private String shiftName;    // 班次名称，如：早班、中班、晚班
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime startTime; // 上班时间，如：08:00:00
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime endTime;   // 下班时间，如：16:00:00
    private String description;  // 班次描述
    private Integer cycleType;   // 轮班类型：1-每天固定班次，2-轮班制
    private Integer dayOrder;    // 班次顺序，用于轮班时的排序
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;  // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;  // 更新时间
}