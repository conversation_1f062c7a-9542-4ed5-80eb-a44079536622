package com.demo.service.impl;

import com.demo.entity.Attendance;
import com.demo.entity.Schedule;
import com.demo.entity.VO.AttendanceScheduleVO;
import com.demo.entity.VO.AttendanceVO;
import com.demo.entity.VO.ScheduleVO;
import com.demo.enums.AttendanceEnum;
import com.demo.service.AttendanceScheduleService;
import com.demo.service.AttendanceService;
import com.demo.service.ScheduleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AttendanceScheduleServiceImpl implements AttendanceScheduleService {

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private AttendanceService attendanceService;

    @Override
    public List<AttendanceScheduleVO> getEmployeeAttendanceSchedule(String userName, LocalDateTime startDate, LocalDateTime endDate) {
        // 获取排班信息
        //List<ScheduleVO> schedules = scheduleService.getEmployeeSchedule(userId, startDate, endDate);
        // 获取排班信息
        List<ScheduleVO> schedules = scheduleService.getEmployeeScheduleByUserName(userName, startDate, endDate);
        // 获取考勤信息
        //List<Attendance> attendances = attendanceService.getEmployeeAttendance(userId, startDate, endDate);
        List<Attendance> attendances = attendanceService.getEmployeeAttendance(userName, startDate, endDate);
        Map<Integer, Attendance> attendanceMap = attendances.stream()
                .collect(Collectors.toMap(Attendance::getScheduleId, a -> a));

        // 组合数据
        return schedules.stream().map(schedule -> {
            AttendanceScheduleVO vo = new AttendanceScheduleVO();
            // 复制排班信息
            BeanUtils.copyProperties(schedule, vo);
            // 设置考勤信息
            Attendance attendance = attendanceMap.get(schedule.getId());
            if (attendance != null) {
                AttendanceVO attendanceVO = new AttendanceVO();
                BeanUtils.copyProperties(attendance, attendanceVO);
                setAttendanceStatus(attendanceVO, attendance.getStatus().getCode());
                vo.setAttendance(attendanceVO);
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public AttendanceScheduleVO handleClock(Integer userId) {
        // 先获取当前时间的排班信息
        Schedule schedule = scheduleService.getTodaySchedule(userId, LocalDateTime.now());
        if (schedule == null) {
            throw new RuntimeException("当前时间没有排班");
        }
        // 转换为VO
        ScheduleVO scheduleVO = new ScheduleVO();
        BeanUtils.copyProperties(scheduleVO, schedule);
        // 处理打卡
        Attendance attendance = attendanceService.clock(userId);
        // 组合数据
        AttendanceScheduleVO vo = new AttendanceScheduleVO();
        BeanUtils.copyProperties(scheduleVO, vo);
        AttendanceVO attendanceVO = new AttendanceVO();
        BeanUtils.copyProperties(attendance, attendanceVO);
        setAttendanceStatus(attendanceVO, attendance.getStatus().getCode());
        vo.setAttendance(attendanceVO);

        return vo;
    }

    // 设置考勤状态描述
    private void setAttendanceStatus(AttendanceVO vo, Integer status) {
        switch (status) {
            case 0:
                vo.setStatus("正常");
                vo.setStatusCode("success");
                break;
            case 1:
                vo.setStatus("迟到");
                vo.setStatusCode("warning");
                break;
            case 2:
                vo.setStatus("早退");
                vo.setStatusCode("warning");
                break;
            case 3:
                vo.setStatus("缺勤");
                vo.setStatusCode("danger");
                break;
            case 4:
                vo.setStatus("请假");
                vo.setStatusCode("info");
                break;
        }
    }

    @Override
    public Map<String, Object> getAttendanceStats(Integer userId, LocalDateTime startDate, LocalDateTime endDate) {
        List<Attendance> attendances = attendanceService.getEmployeeAttendance(userId, startDate, endDate);

        Map<String, Object> stats = new HashMap<>();
        stats.put("total", attendances.size());
        stats.put("normal", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.NORMAL).count());
        stats.put("late", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.BE_LATE).count());
        stats.put("early", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.LEAVE_EARLY).count());
        stats.put("absent", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.ABSENCE_FROM_DUTY).count());
        stats.put("leave", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.LEAVE).count());

        return stats;
    }
} 