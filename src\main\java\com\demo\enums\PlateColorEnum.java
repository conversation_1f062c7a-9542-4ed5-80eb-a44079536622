package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 车牌颜色相关的枚举
 * <AUTHOR>
 */
@Getter
public enum PlateColorEnum implements BaseEnum {
    NO_LICENSE_PLATE(-1, "未知颜色", null),
    BLUE(0, "黄色车牌", null),
    YELLOW(1, "白色车牌", null),
    WHITE(2, "绿色车牌", null),
    BLACK(3, "蓝色车牌", null),
    GREEN(4, "红色车牌", null);

    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    private final String color;

    PlateColorEnum(int code, String desc, String color) {
        this.code = code;
        this.desc = desc;
        this.color = color;
    }

    public static PlateColorEnum of(int code) {
        for (PlateColorEnum plateColor : values()) {
            if (plateColor.getCode() == code) {
                return plateColor;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static PlateColorEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }
}
