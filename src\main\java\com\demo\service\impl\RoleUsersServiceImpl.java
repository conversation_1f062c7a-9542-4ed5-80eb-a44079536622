package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.RoleUsers;
import com.demo.mapper.RoleUsersMapper;
import com.demo.service.RoleUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class RoleUsersServiceImpl extends ServiceImpl<RoleUsersMapper, RoleUsers> implements RoleUsersService{
@Autowired
RoleUsersMapper roleUsersMapper;
    @Override
    public List<RoleUsers> selectRoleId(int id) {
        QueryWrapper<RoleUsers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id",id);
        return roleUsersMapper.selectList(queryWrapper);
    }
}
