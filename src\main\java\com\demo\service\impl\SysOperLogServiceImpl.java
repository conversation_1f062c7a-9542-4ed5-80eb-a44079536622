package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.LogDTO;
import com.demo.entity.SysOperLog;
import com.demo.mapper.SysOperLogMapper;
import com.demo.service.SysOperLogService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog> implements SysOperLogService {
    @Autowired
    SysOperLogMapper sysOperLogMapper;

    @Override
    public SaResult selectLog(LogDTO logDTO) {
        IPage<SysOperLog> page = new Page<>();
        page.setCurrent(logDTO.getCurPage());
        page.setSize(logDTO.getPageSize());
        QueryWrapper<SysOperLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(logDTO.getTitle()), "title", logDTO.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(logDTO.getOperName()), "oper_name", logDTO.getOperName());
        queryWrapper.like(StringUtils.isNotBlank(logDTO.getBusinessType()), "business_type", logDTO.getBusinessType());
        queryWrapper.between(logDTO.getStartTime() != null && logDTO.getEndTime() != null, "oper_time", logDTO.getStartTime(), logDTO.getEndTime());
        queryWrapper.orderByDesc("oper_time");
        IPage<SysOperLog> sysOperLogIPage = sysOperLogMapper.selectPage(page, queryWrapper);
        return SaResult.data(sysOperLogIPage);
    }
}
