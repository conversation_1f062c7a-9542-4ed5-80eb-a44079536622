package com.demo.entity.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 精准劝导表
 */
@Data
public class AccuratePersuasionDTO extends PageBean{
    /**
     * 违法类型
     */
    private String illegalName;

    /**
     * 预处理人员姓名
     */
    private String userName;

    /**
     * 处理方式(打电话还是上门)
     */
    private String disposalMethod;

    /**
     * 处理状态(0未处理，1已处理)
     */
    private Integer disposalStatus;
    /**
     * 实际处理人员姓名
     */
    private String actualProcessingName;

    /**
     * 市
     */
    private String city;

    /**
     * 县（区）
     */
    private String county;

    /**
     * 乡村（镇）
     */
    private String township;

    /**
     * 村
     */
    private String hamlet;

    /**
     * 点位
     */
    private String site;

    /**
     * 处理开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processingStartTime;
    /**
     * 处理结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processingEndTime;

    /**
     * 违法开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 违法结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
