package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AttendanceEnum implements BaseEnum {

    NORMAL(0, "正常","#00FFFF" ), //绿色
    BE_LATE(1, "迟到", "#FF0000"), //红色
    LEAVE_EARLY(2, "早退", "#FF0000"), //红色
    ABSENCE_FROM_DUTY(3, "缺勤", "#FF0000"), //红色
    LEAVE(4, "请假", "#00FF00"), // 青色
    REPEAT_CHECK_IN(5, "重复签到", "#FFFF00"), // 黄色
    REPEAT_CHECK_OUT(6, "重复签退", "#FFFF00"), // 黄色
    CHECK_OUT(7, "正常签退", "#00FFFF"); // 绿色

    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    private final String color;

    AttendanceEnum(int code, String desc, String color) {
        this.code = code;
        this.desc = desc;
        this.color = color;
    }

    public static AttendanceEnum of(int code) {
        for (AttendanceEnum attendanceEnum : values()) {
            if (attendanceEnum.getCode() == code) {
                return attendanceEnum;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static AttendanceEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }
}
