package com.demo.entity.DTO;


import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DeviceDTO extends PageBean {
    /**
     * 设备名字
     */
    private String deviceName;
    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备编号
     */
    private String equipmentNumber;

    /**
     * 维护人员电话
     */
    private String maintainerPhone;

    /**
     * 流密钥
     */
    private String streamKey;

    /**
     * 设备IP
     */
    private String ip;
    /**
     * 状态（是否正常）0正常1不正常
     */
    private Integer state;
    /**
     * 市
     */
    private String city;

    /**
     * 县（区）
     */
    private String county;

    /**
     * 乡（镇）
     */
    private String township;
    /**
     * 村
     */
    private String hamlet;

    /**
     * 点
     */
    private String site;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 是否为开发模式
     */
    private Boolean devMode;

    /**
     * 设备地址信息
     */
    private String addressText;

    /**
     * 车辆来向方向的摄像头RTSP地址
     */
    private String cameraUrl1;

    /**
     * 车辆去向方向的摄像头RTSP地址
     */
    private String cameraUrl2;

    /**
     * 来向区域画面裁剪
     */
    private Integer[] clipRect1;

    /**
     * 去向区域画面裁剪
     */
    private Integer[] clipRect2;

    /**
     * 来向区域碰线坐标
     */
    private Integer[] line1;

    /**
     * 去向区域碰线坐标
     */
    private Integer[] line2;

    /**
     * 没有带头盔的识别阈值
     */
    private Double nohelmetThreshold;

    /**
     * 雨棚识别的阈值
     */
    private Double weatherShieldThreshold;

    /**
     * 劝导行为识别有效期（秒）
     */
    private Integer persuadTimeDuration;

    /**
     * 劝导员人脸识别间隔（秒）
     */
    private Integer advicerFaceRecogDuration;

    /**
     * 劝导员人脸识别阈值
     */
    private Double faceRecogDistanceThreshold;

    /**
     * 前端加载当前配置信息间隔（秒）
     */
    private Integer loadParamInterval;

    /**
     * 前端加载人脸数据库间隔（秒）
     */
    private Integer loadFaceDatabaseInterval;

    /**
     * 跟踪车辆ID的最长时间（秒）
     */
    private Integer bikeTrackidLife;

    /**
     * 是否在设备上保存违法车辆视频
     */
    private Boolean saveViolateVideo;

    /**
     * 记录违法视频前后的时长（秒）
     */
    private Integer startEndTime;

    /**
     * 违法视频帧率
     */
    private Integer videoFps;

    /**
     * 同一时间检测到违法车辆播报次数映射
     */
    private String announceTimes;

    /**
     * 最大连续播报同一音频次数
     */
    private Integer maxAnnounceTimes;

    /**
     * 同一音频播报间隔（秒）
     */
    private Integer announceTimeout;

    /**
     * 语音播报音量控制
     */
    private String announceVolume;

    /**
     * 开始播报时间
     */
    private Integer unmuteStart;

    /**
     * 结束播报时间
     */
    private Integer unmuteEnd;

    /**
     * 背向车辆收集车牌图像最大数量
     */
    private Integer bikePlatesQuantity;

    /**
     * 提交失败后重试间隔时间列表
     */
    private Integer[] retryIntervals;

    /**
     * 上报设备在线状态的间隔时间（秒）
     */
    private Integer onlineStatusInterval;

    /**
     * 违法车辆检测是否以背面碰线优先
     */
    private Boolean backwardPriority;

    /**
     * 模型文件信息
     */
    private String modelFile;
    /**
     * 是否立即更新
     */
    private Boolean urgent;
}
