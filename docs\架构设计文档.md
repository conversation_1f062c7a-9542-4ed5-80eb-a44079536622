# 农村两轮车监控系统 - 分布式架构设计文档

## 1. 架构概述

### 1.1 设计目标
- 支持GB28181-2022国标协议
- 解决摄像机与主服务器网络不通问题
- 实现分布式负载均衡
- 保持现有业务逻辑不变

### 1.2 整体架构
```
用户端 → 主服务器 → 次服务器(1-8) → 摄像机
         ↓           ↓
    业务逻辑处理   媒体流处理
    权限控制      GB28181协议
    数据存储      本地录制
```

## 2. 网络拓扑设计

### 2.1 网络分层
```
┌─────────────────┐
│   用户访问层     │ ← 用户通过互联网访问
└─────────────────┘
         │
┌─────────────────┐
│   主服务器层     │ ← 业务逻辑、权限控制、数据库
└─────────────────┘
         │
┌─────────────────┐
│   次服务器层     │ ← 8个次服务器，媒体流处理
└─────────────────┘
         │
┌─────────────────┐
│   摄像机网络层   │ ← 各自独立网段，通过GB28181连接
└─────────────────┘
```

### 2.2 网络连接关系
- **用户 ↔ 主服务器**: 互联网连接
- **主服务器 ↔ 次服务器**: 内网连接
- **次服务器 ↔ 摄像机**: 独立网段连接
- **次服务器 ↔ 次服务器下的电脑**: 同网段连接
- **次服务器下的电脑 ↔ 摄像机**: 同网段连接
- **摄像机 ↔ 主服务器**: 不直接连通（通过次服务器转发）
- **次服务器下的电脑 ↔ 主服务器**: 不直接连通（通过次服务器代理）

## 3. 服务器角色定义

### 3.1 主服务器职责
- **业务逻辑处理**: 用户管理、权限控制、数据统计
- **SIP信令服务**: 统一的GB28181设备注册和认证
- **流媒体代理**: 按需从次服务器拉流转发给用户
- **数据库管理**: 设备信息、用户数据、系统配置
- **WebRTC信令**: 语音通话信令服务器和TURN服务器

### 3.2 次服务器职责
- **媒体流处理**: ZLMediaKit处理GB28181媒体流
- **本地录制**: 录制文件存储在次服务器
- **协议转换**: GB28181转RTMP/RTSP/HLS等
- **Nginx代理**: 转发HTTP回调和WebRTC信令到主服务器
- **设备管理**: 管理本网段内的摄像机设备

## 4. 技术架构设计

### 4.1 主服务器技术栈
```
┌─────────────────────────────────────┐
│           Spring Boot应用            │
├─────────────────────────────────────┤
│  SIP信令服务  │  StreamService业务逻辑  │
├─────────────────────────────────────┤
│       ZLMediaKit流媒体服务器          │
├─────────────────────────────────────┤
│  MySQL数据库  │  Redis缓存  │  MinIO存储 │
└─────────────────────────────────────┘
```

### 4.2 次服务器技术栈
```
┌─────────────────────────────────────┐
│            Nginx代理服务             │
├─────────────────────────────────────┤
│         ZLMediaKit媒体服务器          │
├─────────────────────────────────────┤
│      本地录制存储  │  设备状态缓存      │
└─────────────────────────────────────┘
```

## 5. 数据流向设计

### 5.1 设备注册流程
```
摄像机 --GB28181注册--> 次服务器Nginx --转发--> 主服务器SIP服务
                                                    ↓
                                              验证设备信息
                                                    ↓
                                              返回注册成功
                                                    ↓
次服务器ZLMediaKit <--媒体流推送指令-- 主服务器 <--注册响应-- 摄像机
```

### 5.2 用户观看流程
```
用户请求观看 → 主服务器权限验证 → 检查流状态
                    ↓
              首次观看：主服务器从次服务器拉流
                    ↓
              后续观看：复用已有流
                    ↓
              用户离开：观看者-1，无观看者时断流
```

### 5.3 WebRTC语音通话流程
```
次服务器下的电脑 --访问--> 次服务器WebRTC页面 --信令--> 次服务器Nginx --转发--> 主服务器WebRTC服务
                                                                                    ↓
                                                                              建立P2P连接
                                                                                    ↓
                                                                          通过TURN服务器中继
```

## 6. 关键技术实现

### 6.1 GB28181协议处理
- **SIP信令**: 主服务器统一处理设备注册、心跳、目录查询
- **媒体传输**: 次服务器ZLMediaKit处理RTP/RTCP媒体流
- **设备控制**: PTZ控制、录像回放等通过SIP信令实现

### 6.2 流媒体代理机制
- **按需拉流**: 用户观看时触发`addStreamProxy`
- **自动断流**: 无观看者时调用`delStreamProxy`
- **流复用**: 多用户观看同一流时共享连接
- **协议支持**: RTMP、RTSP、HLS、WebRTC多协议输出

### 6.3 负载均衡策略
- **地理位置**: 摄像机就近接入对应次服务器
- **网段划分**: 每个次服务器管理特定IP网段
- **故障转移**: 次服务器故障时可切换到备用服务器

## 7. 配置管理设计

### 7.1 主服务器配置
```yaml
# GB28181和次服务器配置
gb28181:
  enabled: true
  sip-domain: "3402000000"
  sip-password: "12345678"
  edge-servers:
    - id: "edge-server-01"
      name: "次服务器1"
      ip: "*************"
      sip-port: 5060
      media-port-range: "10000-10100"
      camera-network: "************/24"
    - id: "edge-server-02"
      name: "次服务器2"
      ip: "*************"
      camera-network: "************/24"
```

### 7.2 次服务器Nginx配置
```nginx
# HTTP回调代理
location /api/ {
    proxy_pass http://主服务器IP:8081;
    proxy_set_header X-Server-ID "edge-server-01";
    proxy_set_header X-Real-IP $remote_addr;
}

# WebRTC信令代理
location /webrtc-ws {
    proxy_pass http://主服务器IP:8081/webrtc-ws;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
}

# 流媒体服务
location /live/ {
    proxy_pass http://127.0.0.1:8080;
    add_header Access-Control-Allow-Origin *;
}
```

### 7.3 次服务器ZLMediaKit配置
```ini
[general]
mediaServerId=edge-server-01
enable_audio=1

[sip]
enabled=1
port=5060
realm=3402000000
id=34020000001320000001

[http]
port=8080
# 回调地址指向本地nginx
hook.on_publish=http://127.0.0.1/api/on_publish
hook.on_unpublish=http://127.0.0.1/api/on_unpublish
hook.on_play=http://127.0.0.1/api/on_play
hook.on_stop=http://127.0.0.1/api/on_stop

[rtp]
port=10000-10100
```

## 8. 部署架构图

### 8.1 物理部署图
```
                    ┌─────────────┐
                    │   用户端     │
                    └─────────────┘
                           │
                    ┌─────────────┐
                    │   主服务器   │
                    │ Spring Boot │
                    │ ZLMediaKit  │
                    │ MySQL/Redis │
                    │ WebRTC服务  │
                    └─────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  次服务器1   │    │  次服务器2   │    │   ...8个    │
│ ZLMediaKit  │    │ ZLMediaKit  │    │ 次服务器     │
│   Nginx     │    │   Nginx     │    │             │
│ WebRTC页面  │    │ WebRTC页面  │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
        │                  │                  │
    ┌───┴───┐          ┌───┴───┐          ┌───┴───┐
    │       │          │       │          │       │
┌─────┐ ┌─────┐    ┌─────┐ ┌─────┐    ┌─────┐ ┌─────┐
│电脑1│ │摄像机│    │电脑2│ │摄像机│    │电脑8│ │摄像机│
│     │ │网段1 │    │     │ │网段2 │    │     │ │网段8 │
└─────┘ └─────┘    └─────┘ └─────┘    └─────┘ └─────┘
```

## 9. 安全设计

### 9.1 网络安全
- **网络隔离**: 摄像机网段与主服务器物理隔离
- **访问控制**: 只允许必要的端口和协议通信
- **防火墙规则**: 严格控制各层之间的访问权限

### 9.2 认证安全
- **GB28181认证**: SIP摘要认证机制
- **设备白名单**: 只允许注册的设备接入
- **传输加密**: 支持SRTP加密传输

## 10. 性能优化设计

### 10.1 流媒体优化
- **按需拉流**: 避免无效的流传输
- **流复用**: 多用户共享同一流连接
- **码率自适应**: 根据网络状况调整码率
- **缓存策略**: 关键帧缓存提高播放体验

### 10.2 系统性能优化
- **连接池**: 数据库和Redis连接池
- **异步处理**: 非关键操作异步执行
- **负载均衡**: 8个次服务器分担处理压力
- **监控告警**: 实时监控系统性能指标

## 11. 故障处理设计

### 11.1 故障检测
- **心跳机制**: 定期检测次服务器状态
- **流状态监控**: 监控流的推送和播放状态
- **设备在线检测**: GB28181心跳检测设备状态

### 11.2 故障恢复
- **自动重连**: 网络中断后自动重新建立连接
- **流切换**: 次服务器故障时切换到备用服务器
- **数据备份**: 重要配置和数据定期备份

## 12. 扩展性设计

### 12.1 水平扩展
- **次服务器扩展**: 可根据需要增加更多次服务器
- **摄像机扩展**: 每个次服务器可管理更多摄像机
- **用户扩展**: 主服务器可支持更多并发用户

### 12.2 功能扩展
- **协议扩展**: 支持更多视频协议
- **AI功能**: 集成视频分析和智能识别
- **云存储**: 支持云端录制和存储
