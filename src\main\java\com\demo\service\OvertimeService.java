package com.demo.service;

import com.demo.entity.Device;
import com.demo.entity.Overtime;
import com.demo.entity.Users;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 加班记录Service接口
 */
public interface OvertimeService {
    
    /**
     * 记录用户加班
     * @param userId 用户ID
     * @param userName 用户姓名
     * @param device 设备信息
     * @param time 打卡时间
     * @return 加班记录
     */
    Overtime recordOvertime(Integer userId, String userName, Device device, LocalDateTime time);
    
    /**
     * 记录用户加班（查找专职人员）
     * @param device 设备信息
     * @param time 打卡时间
     * @return 加班记录
     */
    Overtime recordOvertimeForFulltimeStaff(Device device, LocalDateTime time);
    
    /**
     * 获取用户当天的加班记录
     * @param userId 用户ID
     * @param date 日期
     * @return 加班记录列表
     */
    List<Overtime> getUserOvertimeByDate(Integer userId, LocalDate date);
    
    /**
     * 获取指定时间范围内的加班记录
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 加班记录列表
     */
    List<Overtime> getUserOvertimeByTimeRange(Integer userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取指定时间范围内的所有加班记录（不限制用户）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 加班记录列表
     */
    List<Overtime> getAllOvertimesByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取指定区域和时间范围内的加班记录
     * @param city 市
     * @param county 县
     * @param township 镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 加班记录列表
     */
    List<Overtime> getOvertimesByAreaAndDateRange(
            String city, String county, String township, String hamlet, String site,
            LocalDate startDate, LocalDate endDate);
} 