spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************
    username: root
    password: YBda1234.
  data:
    redis:
      host: **********
      port: 16379
      password: ybDa_2023Data.
      database: 1
#minio配置(下面四个都要替换成自己的)
minio:
  endpoint: https://file.yibindaoan.com  # Minio服务所在地址，改成自己的minIO服务地址
  bucketName: countryside # 存储桶名称（可改）
  accessKey: ybda # minIO账号
  secretKey: YbDa2023. # minIO密码
############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
#  # 配置 Sa-Token 单独使用的 Redis 连接
#  alone-redis:
#    # Redis数据库索引（默认为0）
#    database: 1
#    # Redis服务器地址
#    host: ***************
#    password: ybda1234
#    # Redis服务器连接端口
#    port: 6379
#    # 连接超时时间
#    timeout: 10s

server:
  # 端口
  port: 8081
