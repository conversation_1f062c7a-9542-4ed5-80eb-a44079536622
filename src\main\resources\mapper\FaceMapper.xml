<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.FaceMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Face">
    <!--@mbg.generated-->
    <!--@Table `face`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="face_url" jdbcType="LONGVARCHAR" property="faceUrl" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `face_url`, `user_id`, `user_name`, `create_time`
  </sql>
</mapper>