package com.demo.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 异步HTTP请求工具类
 * 使用 Spring RestTemplate 进行异步HTTP请求，特别是文件上传
 * 复用 IllegalRecordsServiceImpl 中的 RestTemplate 逻辑
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class AsyncHttpClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 创建配置了超时的 RestTemplate
     */
    private RestTemplate createRestTemplateWithTimeout(int connectTimeout, int readTimeout) {
        RestTemplate template = new RestTemplate();
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        template.setRequestFactory(factory);
        return template;
    }
    
    /**
     * 异步提交multipart表单数据（包含文件）
     * 使用 RestTemplate 实现，复用现有的文件上传逻辑
     * 
     * @param url 请求URL
     * @param params 普通参数
     * @param files 文件数组
     * @return CompletableFuture<String> 异步结果
     */
    public CompletableFuture<String> postMultipartAsync(String url, 
                                                       Map<String, Object> params,
                                                       MultipartFile[] files) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建配置了超时的 RestTemplate
                RestTemplate asyncRestTemplate = createRestTemplateWithTimeout(10000, 30000);
                
                // 构建 MultiValueMap，复用 IllegalRecordsServiceImpl 中的逻辑
                MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                
                // 添加普通参数
                if (params != null) {
                    for (Map.Entry<String, Object> entry : params.entrySet()) {
                        body.add(entry.getKey(), entry.getValue());
                    }
                }
                
                // 添加文件，使用与 IllegalRecordsServiceImpl 相同的方式
                if (files != null) {
                    for (MultipartFile file : files) {
                        if (file != null && !file.isEmpty()) {
                            try {
                                // 使用 ByteArrayResource，与 IllegalRecordsServiceImpl 中的方式一致
                                body.add("files", new ByteArrayResource(Objects.requireNonNull(file.getBytes())) {
                                    @Override
                                    public String getFilename() {
                                        return file.getOriginalFilename();
                                    }
                                });
                            } catch (IOException e) {
                                log.error("文件处理失败: {}", e.getMessage());
                                throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
                            }
                        }
                    }
                }
                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
                // 构建日志信息
                StringBuilder fileInfo = new StringBuilder();
                log.info("开始构建文件信息日志，body总字段数: {}", body.size());

                int totalFileCount = 0;
                // 遍历所有字段，查找文件
                for (Map.Entry<String, List<Object>> entry : body.entrySet()) {
                    String fieldName = entry.getKey();
                    List<Object> values = entry.getValue();

                    // 统计该字段下的文件数量
                    int fieldFileCount = 0;
                    for (Object obj : values) {
                        if (obj instanceof Resource file) {
                            totalFileCount++;
                            fieldFileCount++;
                            try {
                                fileInfo.append("[").append(fieldName).append("]").append(file.getFilename())
                                        .append("(").append(file.contentLength()).append(" bytes); ");
                            } catch (IOException e) {
                                fileInfo.append("[").append(fieldName).append("]").append(file.getFilename())
                                        .append("(unknown size); ");
                            }
                        }
                    }
                    log.debug("字段 '{}' 包含 {} 个文件", fieldName, fieldFileCount);
                }

                if (totalFileCount == 0) {
                    fileInfo.append("未找到任何文件");
                    log.warn("请求体中未找到任何文件资源");
                } else {
                    log.info("总共找到 {} 个文件", totalFileCount);
                }

                // 打印请求信息
                log.info("异步HTTP请求即将发送 - URL: {}", url);
                log.info("请求参数详情 - tipsText={}, is_coming1={}, illegal_zone2={}, illegal_zone1={}, is_coming2={}",
                        body.getFirst("tipsText"),
                        body.getFirst("is_coming1"),
                        body.getFirst("illegal_zone2"),
                        body.getFirst("illegal_zone1"),
                        body.getFirst("is_coming2"));
                log.info("文件详情: {}", fileInfo.toString());

                // 在现有文件详情日志附近添加更明确的文件名打印
                if (files != null && files.length > 0) {
                    StringBuilder fileNames = new StringBuilder();
                    for (int i = 0; i < files.length; i++) {
                        if (files[i] != null && !files[i].isEmpty()) {
                            fileNames.append(files[i].getOriginalFilename());
                            if (i < files.length - 1) {
                                fileNames.append(", ");
                            }
                        }
                    }
                    log.info("上传的文件名列表: {}", fileNames.toString());
                }

                log.info("=== 请求文件信息 ===");
                if (files != null) {
                    for (int i = 0; i < files.length; i++) {
                        if (files[i] != null && !files[i].isEmpty()) {
                            log.info("文件[{}]: 名称={}, 大小={} bytes, 类型={}",
                                    i,
                                    files[i].getOriginalFilename(),
                                    files[i].getSize(),
                                    files[i].getContentType());
                        }
                    }
                }
                log.info("===================");

                // 发送请求
                String response = asyncRestTemplate.postForObject(url, requestEntity, String.class);
//                log.info("异步HTTP请求成功: {}", response);
                return response;
                
            } catch (RestClientException e) {
                log.error("异步HTTP请求失败: {}", e.getMessage());
                throw new RuntimeException("异步HTTP请求失败: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("异步HTTP请求异常: {}", e.getMessage());
                throw new RuntimeException("异步HTTP请求异常: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 异步提交JSON数据
     * 使用 RestTemplate 实现
     * 
     * @param url 请求URL
     * @param jsonData JSON字符串
     * @return CompletableFuture<String> 异步结果
     */
    public CompletableFuture<String> postJsonAsync(String url, String jsonData) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建配置了超时的 RestTemplate
                RestTemplate asyncRestTemplate = createRestTemplateWithTimeout(10000, 30000);
                
                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<>(jsonData, headers);
                
                // 发送请求
                String response = asyncRestTemplate.postForObject(url, requestEntity, String.class);
                log.info("异步JSON请求成功: {}", response);
                return response;
                
            } catch (RestClientException e) {
                log.error("异步JSON请求失败: {}", e.getMessage());
                throw new RuntimeException("异步JSON请求失败: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("异步JSON请求异常: {}", e.getMessage());
                throw new RuntimeException("异步JSON请求异常: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 异步提交multipart表单数据（包含文件）- 简化版本
     * 直接使用注入的 RestTemplate
     * 
     * @param url 请求URL
     * @param body 已构建好的 MultiValueMap
     * @return CompletableFuture<String> 异步结果
     */
    public CompletableFuture<String> postMultipartAsync(String url, MultiValueMap<String, Object> body) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
                
                // 发送请求，使用注入的 RestTemplate
                String response = restTemplate.postForObject(url, requestEntity, String.class);
                log.info("异步HTTP请求成功: {}", response);
                return response;
                
            } catch (RestClientException e) {
                log.error("异步HTTP请求失败: {}", e.getMessage());
                throw new RuntimeException("异步HTTP请求失败: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("异步HTTP请求异常: {}", e.getMessage());
                throw new RuntimeException("异步HTTP请求异常: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 构建文件上传的 MultiValueMap
     * 复用 IllegalRecordsServiceImpl 中的逻辑
     * 
     * @param params 普通参数
     * @param files 文件数组
     * @param fileFieldName 文件字段名
     * @return MultiValueMap<String, Object>
     */
    public MultiValueMap<String, Object> buildMultipartBody(Map<String, String> params, 
                                                           MultipartFile[] files, 
                                                           String fileFieldName) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        // 添加普通参数
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                body.add(entry.getKey(), entry.getValue());
            }
        }
        
        // 添加文件
        if (files != null) {
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    try {
                        body.add(fileFieldName, new ByteArrayResource(Objects.requireNonNull(file.getBytes())) {
                            @Override
                            public String getFilename() {
                                return file.getOriginalFilename();
                            }
                        });
                    } catch (IOException e) {
                        log.error("文件处理失败: {}", e.getMessage());
                        throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
                    }
                }
            }
        }
        
        return body;
    }
}
