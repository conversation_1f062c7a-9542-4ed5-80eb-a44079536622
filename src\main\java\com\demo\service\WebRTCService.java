package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * WebRTC信令服务
 * 负责管理WebRTC通话的房间和信令交换
 */
public interface WebRTCService {
    
    /**
     * 创建通话房间
     */
    SaResult createRoom(String roomId);
    
    /**
     * 加入通话房间
     */
    SaResult joinRoom(String roomId, String clientId);
    
    /**
     * 离开通话房间
     */
    SaResult leaveRoom(String roomId, String clientId);
    
    /**
     * 发送offer
     */
    SaResult sendOffer(String roomId, String fromClientId, String sdp);
    
    /**
     * 发送answer
     */
    SaResult sendAnswer(String roomId, String fromClientId, String sdp);
    
    /**
     * 发送ICE候选者
     */
    SaResult sendIceCandidate(String roomId, String fromClientId, Map<String, Object> candidate);
    
    /**
     * 获取房间内所有客户端
     */
    Set<String> getRoomClients(String roomId);
    /**
     * 请求语音通话
     */
    SaResult requestWebRTC(String roomId);
}