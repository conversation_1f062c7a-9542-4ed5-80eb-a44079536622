package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.ExamineFaceDTO;
import com.demo.entity.ExamineFace;
import org.springframework.web.multipart.MultipartFile;

public interface ExamineFaceService extends IService<ExamineFace>{


    SaResult submitExamineFace(String equipmentNumber, MultipartFile[] image);

    SaResult selectExamineFace(String city, String county, String township, String hamlet, String site, String examine, Integer pageSize, Integer curPage);

    SaResult examineFace(ExamineFaceDTO examineFaceDTO);

    SaResult getUsers(String city, String county, String township, String hamlet, String site);
}
