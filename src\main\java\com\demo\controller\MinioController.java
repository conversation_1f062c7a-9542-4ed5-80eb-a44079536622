package com.demo.controller;


import cn.hutool.core.date.DateUtil;
import com.demo.utils.MinioUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/minio")
public class MinioController {

    @Autowired
    private MinioUtil minioUtil;

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload")
    public String uploadReport(MultipartFile[] files) {
        ArrayList<String> urls = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            String formatDate = DateUtil.formatDate(new Date());    //日期
            String folderName = "审核人脸" + "/" + formatDate + "/" + "宜宾市高县庆符镇西江村西江村劝导站";    //构建文件夹名
            String s = minioUtil.uploadFile(file, fileName, folderName);
            urls.add(s);
        }
        return String.join(",", urls);
    }

    /**
     * 预览文件
     */
    @GetMapping("/preview")
    public String preview(String fileName) {
        return minioUtil.getFileUrl(fileName);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download")
    public void download(String fileName, HttpServletResponse response) {
        minioUtil.download(response, fileName);
    }

    /**
     * 删除文件
     */
    @GetMapping("/delete")
    public String delete(String fileName) {
        minioUtil.delete(fileName);
        return "删除成功";
    }

    /**
     * 批量下载指定文件夹下的所有文件
     *
     * @param folderPath 文件夹路径（必需，自动支持模糊匹配，如"2025-08-0"）
     * @param response HTTP响应对象
     */
    @GetMapping("/batchDownload")
    public void batchDownload(
            @RequestParam String folderPath,
            HttpServletResponse response) {

        try {
            // 参数验证
            if (folderPath == null || folderPath.trim().isEmpty()) {
                throw new IllegalArgumentException("文件夹路径不能为空");
            }

            // 获取符合条件的文件列表（智能模糊匹配）
            List<String> fileList = minioUtil.listObjectsByTimeRange(folderPath);

            if (fileList.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("指定文件夹下没有找到任何文件");
                return;
            }

            // 生成ZIP文件名
            String zipFileName = "batch_download_" + sanitizeFileName(folderPath) + ".zip";

            // 执行批量下载
            minioUtil.downloadMultipleFilesAsZip(fileList, response, zipFileName);

        } catch (IllegalArgumentException e) {
            try {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("参数错误: " + e.getMessage());
            } catch (Exception ignored) {}
        } catch (Exception e) {
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("批量下载失败: " + e.getMessage());
            } catch (Exception ignored) {}
            e.printStackTrace();
        }
    }

    /**
     * 清理文件名中的特殊字符，确保文件名安全
     *
     * @param fileName 原始文件名
     * @return 清理后的安全文件名
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unknown";
        }

        // 替换所有可能导致问题的特殊字符
        return fileName.replace("/", "_")
                .replace("\\", "_")
                .replace(":", "_")
                .replace("*", "_")
                .replace("?", "_")
                .replace("\"", "_")
                .replace("<", "_")
                .replace(">", "_")
                .replace("|", "_")
                .replace("%", "_")  // 避免String.format问题
                .replace(" ", "_")  // 替换空格
                .replaceAll("[\\p{Cntrl}]", "_"); // 替换控制字符
    }
}



