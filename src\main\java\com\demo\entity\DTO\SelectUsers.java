package com.demo.entity.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SelectUsers extends PageBean{
    /**
     * 电话
     */
    private String phone;


    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 所在乡（镇）
     */
    private String township;

    /**
     * 所在村
     */
    private String hamlet;

    /**
     * 点位
     */
    private String site;

    /**
     * 状态（0正常1冻结2删除)
     */
    private Integer state;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
