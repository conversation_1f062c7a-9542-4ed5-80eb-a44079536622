package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.entity.DTO.AccuratePersuasionDTO;
import com.demo.entity.DTO.PageBean;
import com.demo.entity.DTO.SubmitResults;
import com.demo.enums.BusinessType;
import com.demo.service.AccuratePersuasionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;

/**
 * 精准劝导控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/accuratePersuasion")
public class AccuratePersuasionController {
    @Autowired
    private AccuratePersuasionService accuratePersuasionService;

    /**
     * 查看精准劝导列表（PC端）
     */
    @SaCheckPermission("/illegal/log/accuratePersuasion/selectAccuratePersuasionList")
    @Log(title = "查询违法处理列表", businessType = BusinessType.SELECT)
    @GetMapping("selectAccuratePersuasionList")
    public SaResult selectAccuratePersuasionList(AccuratePersuasionDTO accuratePersuasionDTO) {
        return accuratePersuasionService.selectAccuratePersuasionList(accuratePersuasionDTO);
    }
    ///**
    // * 删除精准劝导
    // */
    //@RequestMapping("deleteAccuratePersuasion")
    //public SaResult delete(String uuid) {
    //    return accuratePersuasionService.removeById(uuid) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    //}
    ///**
    // * 批量删除精准劝导
    // */
    //@RequestMapping("deleteAccuratePersuasions")
    //public SaResult deleteBatch(@RequestBody List<String> uuids) {
    //    return accuratePersuasionService.removeBatchByIds(uuids) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    //}

    /**
     * 查看精准劝导列表（移动端）
     */
    @GetMapping("selectAccuratePersuasionListMobile")
    public SaResult selectAccuratePersuasionListMobile(Integer id, PageBean pageBean, Integer disposalStatus) {
        return accuratePersuasionService.selectAccuratePersuasionListMobile(id, pageBean, disposalStatus);
    }

    /**
     * 提交精准劝导结果（移动端）
     */
    @RequestMapping("submitAccuratePersuasion")
    public SaResult submitAccuratePersuasion(MultipartFile[] files, SubmitResults submitResults) {
        return accuratePersuasionService.submitAccuratePersuasion(files, submitResults);
    }

    /**
     * 查询未处理派单数
     */
    @GetMapping("selectDispatchNumber")
    public SaResult selectDispatchNumber(Integer userId) {
        return accuratePersuasionService.selectDispatchNumber(userId);
    }

    /**
     * 返回处理方式选择
     */
    @GetMapping("selectDispatchType")
    public SaResult selectDispatchType() {
        ArrayList<String> list = new ArrayList<>();
        list.add("打电话");
        list.add("发微信");
        list.add("发短信");
        list.add("发邮件");
        return SaResult.data(list);
    }


    /**
     * 下派违法
     */
    @PostMapping("dispatchAccuratePersuasion")
    public SaResult dispatchAccuratePersuasion(@RequestParam(value = "files", required = true) MultipartFile[] files,
                                               @RequestParam(value = "userNames", required = true) String[] userNames,
                                               @RequestParam(value = "userIds", required = true) Integer[] userIds,
                                               @RequestParam(value = "city", required = true) String city,
                                               @RequestParam(value = "county", required = false) String county,
                                               @RequestParam(value = "township", required = false) String township,
                                               @RequestParam(value = "hamlet", required = false) String hamlet,
                                               @RequestParam(value = "site", required = false) String site,
                                               @RequestParam(value = "termTime", required = true) @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date termTime,
                                               @RequestParam(value = "IllegalName", required = false) String IllegalName) {
        return accuratePersuasionService.dispatchAccuratePersuasion(files, userIds, userNames, city, county, township, hamlet, site, termTime, IllegalName);
    }

    /**
     * 隐患上传
     */
    @PostMapping("uploadHidden")
    public SaResult uploadHidden(@RequestParam(value = "files", required = true) MultipartFile[] files,
                                 @RequestParam(value = "userName", required = true) String userName,
                                 @RequestParam(value = "userId", required = true) Integer userId,
                                 @RequestParam(value = "city", required = true) String city,
                                 @RequestParam(value = "county", required = false) String county,
                                 @RequestParam(value = "township", required = false) String township,
                                 @RequestParam(value = "hamlet", required = false) String hamlet,
                                 @RequestParam(value = "site", required = false) String site,
                                 @RequestParam(value = "IllegalName", required = false) String IllegalName) {
        return accuratePersuasionService.uploadHidden(files, userId, userName, city, county, township, hamlet, site, IllegalName);
    }


}

