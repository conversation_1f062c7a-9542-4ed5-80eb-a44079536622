package com.demo.entity.DTO;

import com.demo.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SelectIllegalRecords extends PageBean {
    /**
     * 违法类型（0正常车辆,1非机动车驾乘人员未佩戴头盔、2摩托车超员、3三轮车违法载人）
     */
    private TrafficEnum illegalType;

    /**
     * 是否有车牌，有1无0
     */
    private Integer involvePlate;

    /**
     * 车牌颜色
     */
    private PlateColorEnum plateColor;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 车辆类型
     */
    private VehicleTypeEnum vehicleType;

    /**
     * 乘坐人数（大于）
     */
    private Integer numberOfPassengers;
    /**
     * 是否进行劝导动作(0正常车辆不进行劝导，8已进行劝导，9未进行劝导)
     */
    private PersuasionEnum persuasiveBehavior;

    ///**
    // * 需要手动指派（未找到对应位置的成员）0需要1不需要
    // */
    //private AssignEnum handMovement;


    /**
     * 处理状态(12未处理，14已下派劝导员，13劝导员已处理)
     */
    private HandleEnum disposalStatus;

    /**
     * 市
     */
    private String city;

    /**
     * 县（区）
     */
    private String county;

    /**
     * 乡（镇）
     */
    private String township;

    /**
     * 村
     */
    private String hamlet;

    /**
     * 点
     */
    private String site;

    /**
     * 是否修改
     */
    private Integer whetherToModify;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
