package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.AccuratePersuasion;
import com.demo.entity.DTO.AccuratePersuasionDTO;
import com.demo.entity.DTO.PageBean;
import com.demo.entity.DTO.SubmitResults;
import com.demo.entity.IllegalRecords;
import com.demo.entity.Users;
import com.demo.enums.HandleEnum;
import com.demo.mapper.AccuratePersuasionMapper;
import com.demo.mapper.IllegalRecordsMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.AccuratePersuasionService;
import com.demo.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

import static com.demo.enums.HandleEnum.TREATED;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccuratePersuasionServiceImpl extends ServiceImpl<AccuratePersuasionMapper, AccuratePersuasion> implements AccuratePersuasionService {
    @Autowired
    AccuratePersuasionMapper accuratePersuasionMapper;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    IllegalRecordsMapper illegalRecordsMapper;
    @Autowired
    UsersMapper usersMapper;

    @Override
    public SaResult selectAccuratePersuasionList(AccuratePersuasionDTO accuratePersuasionDTO) {
        List<String> roleList = StpUtil.getRoleList(); // 获取：当前账号的角色集合
        int loginIdAsInt = StpUtil.getLoginIdAsInt(); // 获取当前会话账号id, 并转换为int类型
        Users user = usersMapper.selectById(loginIdAsInt);
        // 根据用户角色和位置信息确定顶级节点
        boolean hasPermission = true;
        if (roleList.contains("city")) {
            if (StringUtils.isEmpty(user.getCity())) {
                return SaResult.error("请先设置市");
            }
            accuratePersuasionDTO.setCity(user.getCity());
            hasPermission = false;
        }
        if (roleList.contains("county") && hasPermission) {
            if (StringUtils.isEmpty(user.getCounty())) {
                return SaResult.error("请先设置县");
            }
            accuratePersuasionDTO.setCity(user.getCity());
            accuratePersuasionDTO.setCounty(user.getCounty());
            hasPermission = false;
        }
        if (roleList.contains("township") && hasPermission) {
            if (StringUtils.isEmpty(user.getTownship())) {
                return SaResult.error("请先设置乡镇");
            }
            accuratePersuasionDTO.setCity(user.getCity());
            accuratePersuasionDTO.setCounty(user.getCounty());
            accuratePersuasionDTO.setTownship(user.getTownship());
            hasPermission = false;
        }
        if (roleList.contains("hamlet") && hasPermission) {
            if (StringUtils.isEmpty(user.getHamlet())) {
                return SaResult.error("请先设置村");
            }
            accuratePersuasionDTO.setCity(user.getCity());
            accuratePersuasionDTO.setCounty(user.getCounty());
            accuratePersuasionDTO.setTownship(user.getTownship());
            accuratePersuasionDTO.setHamlet(user.getHamlet());
            hasPermission = false;
        }
        if (roleList.contains("site") && hasPermission) {
            if (StringUtils.isEmpty(user.getSite())) {
                return SaResult.error("请先设置点位");
            }
            accuratePersuasionDTO.setCity(user.getCity());
            accuratePersuasionDTO.setCounty(user.getCounty());
            accuratePersuasionDTO.setTownship(user.getTownship());
            accuratePersuasionDTO.setHamlet(user.getHamlet());
            accuratePersuasionDTO.setSite(user.getSite());
            hasPermission = false;
        }
        if (hasPermission) {
            return SaResult.error("没有权限");
        }
        IPage<AccuratePersuasion> page = new Page<>();
        page.setCurrent(accuratePersuasionDTO.getCurPage());
        page.setSize(accuratePersuasionDTO.getPageSize());
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(accuratePersuasionDTO.getIllegalName()), "Illegal_name", accuratePersuasionDTO.getIllegalName());
        queryWrapper.like(StringUtils.isNotBlank(accuratePersuasionDTO.getUserName()), "user_name", accuratePersuasionDTO.getUserName());
        queryWrapper.like(StringUtils.isNotBlank(accuratePersuasionDTO.getDisposalMethod()), "disposal_method", accuratePersuasionDTO.getDisposalMethod());
        queryWrapper.like(StringUtils.isNotBlank(accuratePersuasionDTO.getActualProcessingName()), "actual_processing_name", accuratePersuasionDTO.getActualProcessingName());
        queryWrapper.eq(accuratePersuasionDTO.getDisposalStatus() != null, "disposal_status", accuratePersuasionDTO.getDisposalStatus());
        queryWrapper.between(accuratePersuasionDTO.getProcessingStartTime() != null && accuratePersuasionDTO.getProcessingEndTime() != null,
                "processing_time", accuratePersuasionDTO.getProcessingStartTime(), accuratePersuasionDTO.getProcessingEndTime());
        queryWrapper.eq(StringUtils.isNotBlank(accuratePersuasionDTO.getCity()), "city", accuratePersuasionDTO.getCity());
        queryWrapper.eq(StringUtils.isNotBlank(accuratePersuasionDTO.getCounty()), "county", accuratePersuasionDTO.getCounty());
        queryWrapper.eq(StringUtils.isNotBlank(accuratePersuasionDTO.getTownship()), "township", accuratePersuasionDTO.getTownship());
        queryWrapper.eq(StringUtils.isNotBlank(accuratePersuasionDTO.getHamlet()), "hamlet", accuratePersuasionDTO.getHamlet());
        queryWrapper.eq(StringUtils.isNotBlank(accuratePersuasionDTO.getSite()), "site", accuratePersuasionDTO.getSite());
        queryWrapper.orderByDesc("create_time");
        IPage<AccuratePersuasion> resultPage = accuratePersuasionMapper.selectPage(page, queryWrapper);
        return SaResult.data(resultPage);
    }

    @Override
    public SaResult selectAccuratePersuasionListMobile(Integer id, PageBean pageBean, Integer disposalStatus) {
        IPage<AccuratePersuasion> page = new Page<>();
        page.setCurrent(pageBean.getCurPage());
        page.setSize(pageBean.getPageSize());
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", id);
        queryWrapper.orderByAsc("disposal_status");
        queryWrapper.orderByDesc("create_time");
        queryWrapper.eq("disposal_status", disposalStatus);
        if (disposalStatus == 1) {
            queryWrapper.eq("actual_processing", id);
        }
        IPage<AccuratePersuasion> accuratePersuasionIPage = accuratePersuasionMapper.selectPage(page, queryWrapper);
        return SaResult.data(accuratePersuasionIPage);
    }

    /**
     * 构建地址
     */
    private String buildAddress(AccuratePersuasion accuratePersuasion) {
        StringBuilder address = new StringBuilder(accuratePersuasion.getCity());
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(accuratePersuasion.getCounty())) {
            address.append(accuratePersuasion.getCounty());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(accuratePersuasion.getTownship())) {
            address.append(accuratePersuasion.getTownship());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(accuratePersuasion.getHamlet())) {
            address.append(accuratePersuasion.getHamlet());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(accuratePersuasion.getSite())) {
            address.append(accuratePersuasion.getSite());
        }
        return address.toString();
    }

    private String buildAddress1(IllegalRecords illegalRecords) {
        StringBuilder address = new StringBuilder(illegalRecords.getCity());
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(illegalRecords.getCounty())) {
            address.append(illegalRecords.getCounty());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(illegalRecords.getTownship())) {
            address.append(illegalRecords.getTownship());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(illegalRecords.getHamlet())) {
            address.append(illegalRecords.getHamlet());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(illegalRecords.getSite())) {
            address.append(illegalRecords.getSite());
        }
        return address.toString();
    }


    @Override
    @Transactional
    public SaResult submitAccuratePersuasion(MultipartFile[] files, SubmitResults submitResults) {
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("illegal_records_uuid", submitResults.getIllegalRecordsUuid());
        List<AccuratePersuasion> accuratePersuasions = accuratePersuasionMapper.selectList(queryWrapper);
        ArrayList<String> urls = new ArrayList<>();
        if (files != null) {
            String formatDate = DateUtil.formatDate(accuratePersuasions.get(0).getCreateTime());    //日期
            String address = buildAddress(accuratePersuasions.get(0));    //地址
            String uuid = accuratePersuasions.get(0).getUuid();     //UUID
            String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
            for (MultipartFile file : files) {
                try {
                    String fileName = file.getOriginalFilename();
                    String upload = minioUtil.uploadFile(file, fileName, folderName);
                    urls.add(upload);
                } catch (Exception e) {
                    // 捕获文件上传异常并返回错误结果
                    e.printStackTrace();
                    return SaResult.error("文件上传失败: " + e.getMessage());
                }
            }
        }
        for (AccuratePersuasion accuratePersuasion : accuratePersuasions) {
            accuratePersuasion.setDisposalMethod(submitResults.getDisposalMethod());
            accuratePersuasion.setDisposalStatus(1);
            accuratePersuasion.setActualProcessing(submitResults.getActualProcessing());
            accuratePersuasion.setActualProcessingName(submitResults.getActualProcessingName());
            accuratePersuasion.setRemarks(submitResults.getRemarks());
            accuratePersuasion.setIllegalPersonnelName(submitResults.getIllegalPersonnelName());
            accuratePersuasion.setIllegalPersonnelIdCard(submitResults.getIllegalPersonnelIdCard());
            accuratePersuasion.setIllegalPersonnelPhone(submitResults.getIllegalPersonnelPhone());
            accuratePersuasion.setIllegalPersonnelGender(submitResults.getIllegalPersonnelGender());
            accuratePersuasion.setProcessingTime(new Date());
            // 确保 urls 不为 null
            String imgl = urls == null || urls.isEmpty() ? "" : String.join(",", urls);
            accuratePersuasion.setImgl(imgl);
            accuratePersuasionMapper.updateById(accuratePersuasion);
        }
        QueryWrapper<IllegalRecords> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("uuid", submitResults.getIllegalRecordsUuid());
        IllegalRecords illegalRecords = new IllegalRecords();
        illegalRecords.setDisposalStatus(TREATED);
        illegalRecordsMapper.update(illegalRecords, queryWrapper1);
        return SaResult.ok();
    }

    @Override
    public SaResult selectDispatchNumber(Integer userId) {
        QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("disposal_status", 0);
        return SaResult.data(accuratePersuasionMapper.selectCount(queryWrapper));
    }

    @Override
    public SaResult processingRate() {
        // 获取当前日期
        LocalDateTime now = LocalDateTime.now();

        // 计算近7天的开始和结束日期
        LocalDateTime endDate = now;
        LocalDateTime startDate = now.minusDays(7);

        // 计算上7天的开始和结束日期
        LocalDateTime previousEndDate = startDate.minusDays(1);
        LocalDateTime previousStartDate = previousEndDate.minusDays(7);

        // 查询近7天的处理率
        double currentRate = calculateProcessingRate(startDate, endDate);

        // 查询上7天的处理率
        double previousRate = calculateProcessingRate(previousStartDate, previousEndDate);

        // 计算对比结果
        double rateDifference = currentRate - previousRate;

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("currentRate", String.format("%.2f", currentRate));     //本周处理率
        result.put("previousRate", String.format("%.2f", previousRate));    //上周处理率
        result.put("rateDifference", String.format("%.2f", rateDifference));    // 提升多少
        return SaResult.data(result);
    }

    @Override
    public SaResult getDisposalStatistics(String city, String county, String township, String hamlet, String site, String year) {
        List<Map<String, Object>> taskNumberProcessingNumberProcessingRateByYear = accuratePersuasionMapper.taskNumberProcessingNumberProcessingRateByYear(city, county, township, hamlet, site,year);  //任务数,处置数，处置率
        return SaResult.data(taskNumberProcessingNumberProcessingRateByYear);
    }

    @Override
    public SaResult getDisposalStatisticsByMonth(String city, String county, String township, String hamlet, String site, Date startTime, Date endTime) {
             List<Map<String, Object>> taskNumberProcessingNumberProcessingRateByMonth = accuratePersuasionMapper.taskNumberProcessingNumberProcessingRateByMonth(city, county, township, hamlet, site, startTime, endTime);  //任务数,处置数，处置率
        return SaResult.data(taskNumberProcessingNumberProcessingRateByMonth);
    }


    private double calculateProcessingRate(LocalDateTime startDate, LocalDateTime endDate) {
        // 查询数据库获取处理的数量和总数量
        int total = accuratePersuasionMapper.countTotal(startDate, endDate);
        int processed = accuratePersuasionMapper.countProcessed(startDate, endDate);
        // 计算处理率
        return total == 0 ? 0 : (double) processed / total * 100;
    }


    private SaResult formatForECharts(List<Map<String, Object>> trends, int periods) {
        Map<String, Object> result = new HashMap<>();
        List<String> categories = new ArrayList<>();
        List<Map<String, Object>> series = new ArrayList<>();

        // 初始化数据结构
        Map<String, List<Integer>> dataMap = new HashMap<>();
        for (Map<String, Object> trend : trends) {
            String type = (String) trend.get("illegal_type");
            int period = (int) trend.get("period");
            int count = (int) trend.get("count");

            dataMap.putIfAbsent(type, new ArrayList<>(Collections.nCopies(periods, 0)));
            dataMap.get(type).set(period - 1, count);
        }

        // 构建返回数据
        for (Map.Entry<String, List<Integer>> entry : dataMap.entrySet()) {
            Map<String, Object> seriesData = new HashMap<>();
            seriesData.put("name", entry.getKey());
            seriesData.put("data", entry.getValue());
            series.add(seriesData);
        }

        // 设置类别（如月份、天数）
        for (int i = 1; i <= periods; i++) {
            categories.add(String.valueOf(i));
        }

        result.put("categories", categories);
        result.put("series", series);

        return SaResult.data(result);
    }

    @Override
    public SaResult dispatchAccuratePersuasion(MultipartFile[] files, Integer[] userIds, String[] userNames,
                                               String city, String county, String township, String hamlet, String site,
                                               Date termTime, String illegalName) {
        IllegalRecords illegalRecords = new IllegalRecords();
        illegalRecords.setUuid(IdUtil.simpleUUID());
        illegalRecords.setIllegalName(illegalName);
        illegalRecords.setCity(city);
        illegalRecords.setCounty(county);
        illegalRecords.setTownship(township);
        illegalRecords.setHamlet(hamlet);
        illegalRecords.setSite(site);
        illegalRecords.setCreateTime(new Date());
        String formatDate = DateUtil.formatDate(illegalRecords.getCreateTime());    //日期
        String address = buildAddress1(illegalRecords);    //地址
        String uuid = illegalRecords.getUuid();     //UUID
        String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
        if (files != null && files.length != 0) {
            ArrayList<String> urls = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String upload = minioUtil.uploadFile(file, fileName, folderName);
                urls.add(upload);
            }
            illegalRecords.setPictureUrl(String.join(",", urls));
        }
        illegalRecords.setDisposalStatus(HandleEnum.UNTREATED);
        illegalRecordsMapper.insert(illegalRecords);
        for (int i = 0; i < userIds.length; i++) {
            AccuratePersuasion accuratePersuasion = new AccuratePersuasion();
            accuratePersuasion.setUuid(IdUtil.simpleUUID());
            accuratePersuasion.setIllegalName(illegalName);
            accuratePersuasion.setIllegalRecordsUuid(illegalRecords.getUuid());
            accuratePersuasion.setUserId(userIds[i]);
            accuratePersuasion.setUserName(userNames[i]);
            accuratePersuasion.setDisposalStatus(0);
            accuratePersuasion.setCity(city);
            accuratePersuasion.setCounty(county);
            accuratePersuasion.setTownship(township);
            accuratePersuasion.setHamlet(hamlet);
            accuratePersuasion.setSite(site);
            accuratePersuasion.setLowerFactionType("手动下派");
            accuratePersuasion.setDeadlineTime(termTime);
            accuratePersuasionMapper.insert(accuratePersuasion);
        }
        return SaResult.ok();
    }

    @Override
    public SaResult uploadHidden(MultipartFile[] files, Integer userId, String userName, String city, String county, String township, String hamlet, String site, String illegalName) {
        IllegalRecords illegalRecords = new IllegalRecords();
        illegalRecords.setUuid(IdUtil.simpleUUID());
        illegalRecords.setIllegalName(illegalName);
        illegalRecords.setCity(city);
        illegalRecords.setCounty(county);
        illegalRecords.setTownship(township);
        illegalRecords.setHamlet(hamlet);
        illegalRecords.setSite(site);
        illegalRecords.setCreateTime(new Date());
        String formatDate = DateUtil.formatDate(illegalRecords.getCreateTime());    //日期
        String address = buildAddress1(illegalRecords);    //地址
        String uuid = illegalRecords.getUuid();     //UUID
        String folderName = formatDate + "/" + address + "/" + uuid;    //构建文件夹名
        if (files != null && files.length != 0) {
            ArrayList<String> urls = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String upload = minioUtil.uploadFile(file, fileName, folderName);
                urls.add(upload);
            }
            illegalRecords.setPictureUrl(String.join(",", urls));
        }
        illegalRecords.setDisposalStatus(HandleEnum.UNTREATED);
        illegalRecordsMapper.insert(illegalRecords);
        AccuratePersuasion accuratePersuasion = new AccuratePersuasion();
        accuratePersuasion.setUuid(IdUtil.simpleUUID());
        accuratePersuasion.setIllegalName(illegalName);
        accuratePersuasion.setIllegalRecordsUuid(illegalRecords.getUuid());
        accuratePersuasion.setUserId(userId);
        accuratePersuasion.setUserName(userName);
        accuratePersuasion.setDisposalStatus(0);
        accuratePersuasion.setCity(city);
        accuratePersuasion.setCounty(county);
        accuratePersuasion.setTownship(township);
        accuratePersuasion.setHamlet(hamlet);
        accuratePersuasion.setSite(site);
        accuratePersuasion.setLowerFactionType("隐患上报");
        accuratePersuasion.setDeadlineTime(new  Date());
        accuratePersuasionMapper.insert(accuratePersuasion);
        return SaResult.ok();
    }

    @Override
    public SaResult getDisposalEfficiencyAnalysisByDay(String city, String county, String township, String hamlet, String site, int year, int month, int day) {
        try {
            List<Map<String, Object>> disposalEfficiencyAnalysis = accuratePersuasionMapper.getDisposalEfficiencyAnalysisByDay(city, county, township, hamlet, site, year, month, day);
            // 构建返回结果，包含查询层级信息
            Map<String, Object> result = new HashMap<>();
            result.put("data", disposalEfficiencyAnalysis);
            result.put("total_count", disposalEfficiencyAnalysis.size());
            return SaResult.data(result);
        } catch (Exception e) {
            log.error("查询处置效率分析失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult getDisposalEfficiencyAnalysisByMonth(String city, String county, String township, String hamlet, String site, int year, int month) {
        try {
            // 获取月度汇总数据
            List<Map<String, Object>> monthlyAnalysis = accuratePersuasionMapper.getDisposalEfficiencyAnalysisByMonth(city, county, township, hamlet, site, year, month);
            // 获取每日详细数据
            List<Map<String, Object>> dailyDetails = accuratePersuasionMapper.getDisposalEfficiencyDailyDetailsByMonth(city, county, township, hamlet, site, year, month);
            // 按地域分组每日详细数据
            Map<String, List<Map<String, Object>>> dailyDetailsByRegion = new HashMap<>();
            for (Map<String, Object> dailyDetail : dailyDetails) {
                String region = (String) dailyDetail.get("region");
                if (region != null) {
                    dailyDetailsByRegion.computeIfAbsent(region, k -> new ArrayList<>()).add(dailyDetail);
                }
            }
            // 将每日详细数据合并到月度汇总数据中
            for (Map<String, Object> monthlyData : monthlyAnalysis) {
                String region = (String) monthlyData.get("region");
                List<Map<String, Object>> regionDailyDetails = dailyDetailsByRegion.get(region);
                monthlyData.put("dailyDetails", regionDailyDetails != null ? regionDailyDetails : new ArrayList<>());
            }
            // 构建返回结果，包含查询层级信息
            Map<String, Object> result = new HashMap<>();
            result.put("data", monthlyAnalysis);
            result.put("total_count", monthlyAnalysis.size());

            return SaResult.data(result);
        } catch (Exception e) {
            log.error("查询月度处置效率分析失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult getDisposalEfficiencyAnalysisByYear(String city, String county, String township, String hamlet, String site, int year) {
        try {
            // 获取年度汇总数据
            List<Map<String, Object>> yearlyAnalysis = accuratePersuasionMapper.getDisposalEfficiencyAnalysisByYear(city, county, township, hamlet, site, year);

            // 获取每月详细数据
            List<Map<String, Object>> monthlyDetails = accuratePersuasionMapper.getDisposalEfficiencyMonthlyDetailsByYear(city, county, township, hamlet, site, year);
            // 按地域分组每月详细数据
            Map<String, List<Map<String, Object>>> monthlyDetailsByRegion = new HashMap<>();
            for (Map<String, Object> monthlyDetail : monthlyDetails) {
                String region = (String) monthlyDetail.get("region");
                if (region != null) {
                    monthlyDetailsByRegion.computeIfAbsent(region, k -> new ArrayList<>()).add(monthlyDetail);
                }
            }
            // 将每月详细数据合并到年度汇总数据中
            for (Map<String, Object> yearlyData : yearlyAnalysis) {
                String region = (String) yearlyData.get("region");
                List<Map<String, Object>> regionMonthlyDetails = monthlyDetailsByRegion.get(region);
                yearlyData.put("monthlyDetails", regionMonthlyDetails != null ? regionMonthlyDetails : new ArrayList<>());
            }
            // 构建返回结果，包含查询层级信息
            Map<String, Object> result = new HashMap<>();
            result.put("data", yearlyAnalysis);
            result.put("total_count", yearlyAnalysis.size());
            return SaResult.data(result);
        } catch (Exception e) {
            log.error("查询年度处置效率分析失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }
}
