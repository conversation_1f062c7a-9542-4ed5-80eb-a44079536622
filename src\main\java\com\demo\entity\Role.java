package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 角色表
 */
@Data
@TableName(value = "`role`")
public class Role {
    /**
     * id
     */
    @TableId(value = "role_id", type = IdType.AUTO)
    private Integer roleId;

    /**
     * 角色编号
     */
    @TableField(value = "role_code")
    private String roleCode;

    /**
     * 角色名
     */
    @TableField(value = "role_name")
    private String roleName;
    /**
     * 角色拥有的权限
     */
    @TableField(exist = false)
    private List<Permission> permissionList; // 假设有一个权限列表
}