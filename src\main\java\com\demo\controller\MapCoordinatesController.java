package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.service.MapCoordinatesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 地图控制器
 */
@RestController
@RequestMapping("/map")
public class MapCoordinatesController {
    @Autowired
    MapCoordinatesService mapCoordinatesService;
    /**
     * 获取地点经纬度
     */
    @GetMapping("/getCoordinate")
    public SaResult getCoordinate(
            @RequestParam String city,
            @RequestParam String county) {
        return mapCoordinatesService.getCoordinate(city, county);
    }
}
