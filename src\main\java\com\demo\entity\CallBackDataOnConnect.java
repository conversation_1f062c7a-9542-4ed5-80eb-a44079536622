package com.demo.entity;

import lombok.Data;

/**
 * SRS回调数据
 * <AUTHOR>
 */
@Data
public class CallBackDataOnConnect {
    /**
     * 客户端请求的动作类型
     */
    String action;
    /**
     * 服务器生成的一个唯一标识符
     */
    String client_id;
    /**
     * 客户端的 IP 地址
     */
    String ip;
    /**
     * 虚拟主机名称
     */
    String vhost;
    /**
     * 应用程序名称
     */
    String app;
    /**
     * 流名称
     */
    String stream;
    /**
     * TCURL (TiC URL) 是 RTMP 连接的完整 URL
     */
    String tcUrl;
    /**
     * 页面 URL
     */
    String pageUrl;
    /**
     * 其他参数
     */
    String param;
}
