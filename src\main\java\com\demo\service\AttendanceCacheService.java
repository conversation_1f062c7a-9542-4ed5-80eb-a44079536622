package com.demo.service;

import java.time.LocalDate;
import java.util.List;

/**
 * 考勤缓存服务接口
 * 用于优化考勤统计接口的性能
 */
public interface AttendanceCacheService {

    /**
     * 清理过期缓存
     */
    void cleanExpiredCache();

    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    String getCacheStats();

    /**
     * 手动刷新指定日期的缓存
     * @param date 日期
     * @param city 市
     * @param county 县
     * @param township 镇
     * @param hamlet 村
     * @param site 点位
     */
    void refreshCache(LocalDate date, String city, String county, String township, String hamlet, String site);
}
