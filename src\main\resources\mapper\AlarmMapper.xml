<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.AlarmMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Alarm">
    <!--@mbg.generated-->
    <!--@Table `alarm`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="alarm_type" jdbcType="VARCHAR" property="alarmType" />
      <result column="wf_uuid" jdbcType="VARCHAR" property="wfUuid" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="degree" jdbcType="VARCHAR" property="degree" />
    <result column="target_type" jdbcType="VARCHAR" property="targetType" />
    <result column="know" jdbcType="INTEGER" property="know" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="township" jdbcType="VARCHAR" property="township" />
    <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `alarm_type`, `wf_uuid`,`event_time`, `content`, `degree`, `target_type`, `know`, `create_time`,
    `city`, `county`, `township`, `hamlet`, `site`, `update_time`
  </sql>
</mapper>